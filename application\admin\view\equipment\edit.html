<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

<!--    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Platform_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-platform_id" data-rule="required" data-source="platform/index" class="form-control selectpage" name="row[platform_id]" type="text" value="{$row.platform_id}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Agent_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-agent_id" data-rule="required" data-source="agent/index" class="form-control selectpage" name="row[agent_id]" type="text" value="{$row.agent_id}">
        </div>
    </div>

    -->
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('选择门店')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-hospitals_id" data-rule="required" data-source="hospital/index" class="form-control selectpage" name="row[hospitals_id]" type="text" value="{$row.hospitals_id}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('选择门店场地')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-departments_id" data-rule="required" data-field="deptname"  data-source="departments/index/hospital_id/{$hospital_id}" data-params='{"custom[hospital_id]":{$hospital_id}}' class="form-control selectpage" name="row[departments_id]" type="text" value="{$row.departments_id}">
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Mainname')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-mainname" data-rule="required" class="form-control" name="row[mainname]" type="text" value="{$row.mainname}">
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Hardware_type')}:</label>
        <div class="col-xs-12 col-sm-8">            
            <div class="radio">
            {foreach name="hardwaretypeList" item="vo"}
            <label for="row[hardware_type]-{$key}"><input id="row[hardware_type]-{$key}" name="row[hardware_type]" type="radio" value="{$key}" {in name="key" value="$row.hardware_type"}checked{/in} /> {$vo}</label> 
            {/foreach}
            </div>

        </div>
    </div>




    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Notes')}:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-notes" class="form-control " rows="5" name="row[notes]" cols="50">{$row.notes}</textarea>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">
            
            <div class="radio">
            {foreach name="statusList" item="vo"}
            <label for="row[status]-{$key}"><input id="row[status]-{$key}" name="row[use_status]" type="radio" value="{$key}" {in name="key" value="$row.use_status"}checked{/in} /> {$vo}</label>
            {/foreach}
            </div>

        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>
