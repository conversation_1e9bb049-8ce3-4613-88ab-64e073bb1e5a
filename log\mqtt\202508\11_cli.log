[ 2025-08-11T10:28:36+08:00 ][ error ] 收到消息：dz/pi/getstatus/710001
error
[ 2025-08-11T10:28:36+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710001',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710001","SIMID":"89861118167312012425","CS":"0","LS":"1","SS":"0","BS":"10.2","RSSI":23,"MT":"4","NG":"0"}',
  'timestamp' => '2025-08-11 10:28:36',
)
error
[ 2025-08-11T10:28:36+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T10:28:36+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710001',
  'SIMID' => '89861118167312012425',
  'CS' => '0',
  'LS' => '1',
  'SS' => '0',
  'BS' => '10.2',
  'RSSI' => 23,
  'MT' => '4',
  'NG' => '0',
)
error
[ 2025-08-11T10:28:36+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710001
error
[ 2025-08-11T10:28:36+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-11T10:28:36+08:00 ][ log ] 【状态上报】开始处理地锁 '710001' 的通用状态...
log
[ 2025-08-11T10:28:36+08:00 ][ log ] 【状态上报】成功更新地锁 '710001' 的快照状态到数据库
log
[ 2025-08-11T10:28:36+08:00 ][ log ] 【状态上报】成功记录地锁 '710001' 的状态到日志表
log
[ 2025-08-11T10:28:36+08:00 ][ log ] 【业务触发】检查 '710001' 的状态变化...
log
[ 2025-08-11T10:28:36+08:00 ][ log ] 【业务触发】检测到地锁 '710001' 关锁成功，开始处理订单...
log
[ 2025-08-11T10:28:36+08:00 ][ error ] 收到消息：dz/pi/setack/710001
error
[ 2025-08-11T10:28:36+08:00 ][ error ] array (
  'topic' => 'dz/pi/setack/710001',
  'message' => '{"VER":"0","CMD":"17","CD":"710001","CL":"1","PACKID":8414,"STATUS":"81"}',
  'timestamp' => '2025-08-11 10:28:36',
)
error
[ 2025-08-11T10:28:36+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T10:28:36+08:00 ][ error ] array (
  'VER' => '0',
  'CMD' => '17',
  'CD' => '710001',
  'CL' => '1',
  'PACKID' => 8414,
  'STATUS' => '81',
)
error
[ 2025-08-11T10:28:36+08:00 ][ error ] 收到命令响应消息，待处理: dz/pi/setack/710001
error
[ 2025-08-11T10:28:36+08:00 ][ log ] 【命令响应】收到针对命令包 '8414' 的ACK确认
log
[ 2025-08-11T10:28:36+08:00 ][ log ] 【命令响应】成功更新命令 '8414' 的状态为 'acked'
log
[ 2025-08-11T10:28:58+08:00 ][ error ] 收到消息：dz/pi/getstatus/710001
error
[ 2025-08-11T10:28:58+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710001',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710001","SIMID":"89861118167312012425","CS":"0","LS":"0","SS":"0","BS":"11.3","RSSI":23,"MT":"4","NG":"0"}',
  'timestamp' => '2025-08-11 10:28:58',
)
error
[ 2025-08-11T10:28:58+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T10:28:58+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710001',
  'SIMID' => '89861118167312012425',
  'CS' => '0',
  'LS' => '0',
  'SS' => '0',
  'BS' => '11.3',
  'RSSI' => 23,
  'MT' => '4',
  'NG' => '0',
)
error
[ 2025-08-11T10:28:58+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710001
error
[ 2025-08-11T10:28:58+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-11T10:28:58+08:00 ][ log ] 【状态上报】开始处理地锁 '710001' 的通用状态...
log
[ 2025-08-11T10:28:58+08:00 ][ log ] 【状态上报】成功更新地锁 '710001' 的快照状态到数据库
log
[ 2025-08-11T10:28:58+08:00 ][ log ] 【状态上报】成功记录地锁 '710001' 的状态到日志表
log
[ 2025-08-11T10:28:58+08:00 ][ log ] 【业务触发】检查 '710001' 的状态变化...
log
[ 2025-08-11T10:28:58+08:00 ][ log ] 【业务触发】检测到地锁 '710001' 开锁成功，结束处理订单...
log
[ 2025-08-11T10:28:58+08:00 ][ log ] 打印order
log
[ 2025-08-11T10:28:58+08:00 ][ log ] array (
  'id' => 45,
  'platform_id' => 20,
  'agent_id' => 82,
  'hospital_id' => 48,
  'hospital_fcbl' => 20.0,
  'hospital_price' => '1.00',
  'hospital_hourlong' => 1,
  'hospital_freedt' => 99999,
  'departments_id' => 34,
  'equipment_id' => 2,
  'equipment_info_id' => 0,
  'info' => '总平台-深圳代理商-南山物业-物业北车棚-设备710001-710001',
  'sn' => 'ord2025081110283195782145',
  'user_id' => 5611,
  'money' => '0.00',
  'status' => '1',
  'pay_types' => '0',
  'pay_status' => '0',
  'pay_time' => NULL,
  'createtime' => 1754879316,
  'returntime' => NULL,
  'updatetime' => 1754879311,
  'is_branch' => '1',
  'admin_info' => NULL,
  'timelong' => 0,
  'timelong_fenzhong' => 0,
  'really_money' => '0.00',
  'actreturntime' => NULL,
  'answer_return_time' => 1754924400,
  'overtime' => NULL,
  'overtime_money' => NULL,
  'normal_money' => NULL,
  'charging_rule' => 1,
  'use_status' => 2,
  'nb_goodsid' => '',
)
log
[ 2025-08-11T10:28:58+08:00 ][ error ] 【业务触发】订单(SN:ord2025081110283195782145)结束逻辑处理成功
error
[ 2025-08-11T10:28:59+08:00 ][ error ] 收到消息：dz/pi/setack/710001
error
[ 2025-08-11T10:28:59+08:00 ][ error ] array (
  'topic' => 'dz/pi/setack/710001',
  'message' => '{"VER":"0","CMD":"17","CD":"710001","CL":"0","PACKID":9351,"STATUS":"1"}',
  'timestamp' => '2025-08-11 10:28:59',
)
error
[ 2025-08-11T10:28:59+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T10:28:59+08:00 ][ error ] array (
  'VER' => '0',
  'CMD' => '17',
  'CD' => '710001',
  'CL' => '0',
  'PACKID' => 9351,
  'STATUS' => '1',
)
error
[ 2025-08-11T10:28:59+08:00 ][ error ] 收到命令响应消息，待处理: dz/pi/setack/710001
error
[ 2025-08-11T10:28:59+08:00 ][ log ] 【命令响应】收到针对命令包 '9351' 的ACK确认
log
[ 2025-08-11T10:28:59+08:00 ][ log ] 【命令响应】成功更新命令 '9351' 的状态为 'acked'
log
[ 2025-08-11T10:33:06+08:00 ][ error ] 收到消息：dz/pi/getstatus/710001
error
[ 2025-08-11T10:33:06+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710001',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710001","SIMID":"89861118167312012425","CS":"0","LS":"1","SS":"0","BS":"10.2","RSSI":23,"MT":"4","NG":"0"}',
  'timestamp' => '2025-08-11 10:33:06',
)
error
[ 2025-08-11T10:33:06+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T10:33:06+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710001',
  'SIMID' => '89861118167312012425',
  'CS' => '0',
  'LS' => '1',
  'SS' => '0',
  'BS' => '10.2',
  'RSSI' => 23,
  'MT' => '4',
  'NG' => '0',
)
error
[ 2025-08-11T10:33:06+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710001
error
[ 2025-08-11T10:33:06+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-11T10:33:06+08:00 ][ log ] 【状态上报】开始处理地锁 '710001' 的通用状态...
log
[ 2025-08-11T10:33:06+08:00 ][ log ] 【状态上报】成功更新地锁 '710001' 的快照状态到数据库
log
[ 2025-08-11T10:33:06+08:00 ][ log ] 【状态上报】成功记录地锁 '710001' 的状态到日志表
log
[ 2025-08-11T10:33:06+08:00 ][ log ] 【业务触发】检查 '710001' 的状态变化...
log
[ 2025-08-11T10:33:06+08:00 ][ log ] 【业务触发】检测到地锁 '710001' 关锁成功，开始处理订单...
log
[ 2025-08-11T10:33:06+08:00 ][ error ] 收到消息：dz/pi/setack/710001
error
[ 2025-08-11T10:33:06+08:00 ][ error ] array (
  'topic' => 'dz/pi/setack/710001',
  'message' => '{"VER":"0","CMD":"17","CD":"710001","CL":"1","PACKID":41551,"STATUS":"81"}',
  'timestamp' => '2025-08-11 10:33:06',
)
error
[ 2025-08-11T10:33:06+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T10:33:06+08:00 ][ error ] array (
  'VER' => '0',
  'CMD' => '17',
  'CD' => '710001',
  'CL' => '1',
  'PACKID' => 41551,
  'STATUS' => '81',
)
error
[ 2025-08-11T10:33:06+08:00 ][ error ] 收到命令响应消息，待处理: dz/pi/setack/710001
error
[ 2025-08-11T10:33:06+08:00 ][ log ] 【命令响应】收到针对命令包 '41551' 的ACK确认
log
[ 2025-08-11T10:33:06+08:00 ][ log ] 【命令响应】成功更新命令 '41551' 的状态为 'acked'
log
[ 2025-08-11T10:33:24+08:00 ][ error ] 收到消息：dz/pi/getstatus/710001
error
[ 2025-08-11T10:33:24+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710001',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710001","SIMID":"89861118167312012425","CS":"0","LS":"0","SS":"0","BS":"11.4","RSSI":23,"MT":"4","NG":"0"}',
  'timestamp' => '2025-08-11 10:33:24',
)
error
[ 2025-08-11T10:33:24+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T10:33:24+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710001',
  'SIMID' => '89861118167312012425',
  'CS' => '0',
  'LS' => '0',
  'SS' => '0',
  'BS' => '11.4',
  'RSSI' => 23,
  'MT' => '4',
  'NG' => '0',
)
error
[ 2025-08-11T10:33:24+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710001
error
[ 2025-08-11T10:33:24+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-11T10:33:24+08:00 ][ log ] 【状态上报】开始处理地锁 '710001' 的通用状态...
log
[ 2025-08-11T10:33:24+08:00 ][ log ] 【状态上报】成功更新地锁 '710001' 的快照状态到数据库
log
[ 2025-08-11T10:33:24+08:00 ][ log ] 【状态上报】成功记录地锁 '710001' 的状态到日志表
log
[ 2025-08-11T10:33:24+08:00 ][ log ] 【业务触发】检查 '710001' 的状态变化...
log
[ 2025-08-11T10:33:24+08:00 ][ log ] 【业务触发】检测到地锁 '710001' 开锁成功，结束处理订单...
log
[ 2025-08-11T10:33:24+08:00 ][ log ] 打印order
log
[ 2025-08-11T10:33:24+08:00 ][ log ] array (
  'id' => 46,
  'platform_id' => 20,
  'agent_id' => 82,
  'hospital_id' => 48,
  'hospital_fcbl' => 20.0,
  'hospital_price' => '1.00',
  'hospital_hourlong' => 1,
  'hospital_freedt' => 99999,
  'departments_id' => 34,
  'equipment_id' => 2,
  'equipment_info_id' => 0,
  'info' => '总平台-深圳代理商-南山物业-物业北车棚-设备710001-710001',
  'sn' => 'ord2025081110330150589237',
  'user_id' => 5611,
  'money' => '0.00',
  'status' => '1',
  'pay_types' => '0',
  'pay_status' => '0',
  'pay_time' => NULL,
  'createtime' => 1754879586,
  'returntime' => NULL,
  'updatetime' => 1754879581,
  'is_branch' => '1',
  'admin_info' => NULL,
  'timelong' => 0,
  'timelong_fenzhong' => 0,
  'really_money' => '0.00',
  'actreturntime' => NULL,
  'answer_return_time' => 1754924400,
  'overtime' => NULL,
  'overtime_money' => NULL,
  'normal_money' => NULL,
  'charging_rule' => 1,
  'use_status' => 2,
  'nb_goodsid' => '',
)
log
[ 2025-08-11T10:33:24+08:00 ][ error ] 【业务触发】订单(SN:ord2025081110330150589237)结束逻辑处理成功
error
[ 2025-08-11T10:33:24+08:00 ][ error ] 收到消息：dz/pi/setack/710001
error
[ 2025-08-11T10:33:24+08:00 ][ error ] array (
  'topic' => 'dz/pi/setack/710001',
  'message' => '{"VER":"0","CMD":"17","CD":"710001","CL":"0","PACKID":42928,"STATUS":"1"}',
  'timestamp' => '2025-08-11 10:33:24',
)
error
[ 2025-08-11T10:33:24+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T10:33:24+08:00 ][ error ] array (
  'VER' => '0',
  'CMD' => '17',
  'CD' => '710001',
  'CL' => '0',
  'PACKID' => 42928,
  'STATUS' => '1',
)
error
[ 2025-08-11T10:33:24+08:00 ][ error ] 收到命令响应消息，待处理: dz/pi/setack/710001
error
[ 2025-08-11T10:33:24+08:00 ][ log ] 【命令响应】收到针对命令包 '42928' 的ACK确认
log
[ 2025-08-11T10:33:24+08:00 ][ log ] 【命令响应】成功更新命令 '42928' 的状态为 'acked'
log
[ 2025-08-11T10:38:58+08:00 ][ error ] 收到消息：dz/pi/getstatus/710001
error
[ 2025-08-11T10:38:58+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710001',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710001","SIMID":"89861118167312012425","CS":"0","LS":"1","SS":"0","BS":"10.2","RSSI":23,"MT":"4","NG":"0"}',
  'timestamp' => '2025-08-11 10:38:58',
)
error
[ 2025-08-11T10:38:58+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T10:38:58+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710001',
  'SIMID' => '89861118167312012425',
  'CS' => '0',
  'LS' => '1',
  'SS' => '0',
  'BS' => '10.2',
  'RSSI' => 23,
  'MT' => '4',
  'NG' => '0',
)
error
[ 2025-08-11T10:38:58+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710001
error
[ 2025-08-11T10:38:58+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-11T10:38:58+08:00 ][ log ] 【状态上报】开始处理地锁 '710001' 的通用状态...
log
[ 2025-08-11T10:38:58+08:00 ][ log ] 【状态上报】成功更新地锁 '710001' 的快照状态到数据库
log
[ 2025-08-11T10:38:58+08:00 ][ log ] 【状态上报】成功记录地锁 '710001' 的状态到日志表
log
[ 2025-08-11T10:38:58+08:00 ][ log ] 【业务触发】检查 '710001' 的状态变化...
log
[ 2025-08-11T10:38:58+08:00 ][ log ] 【业务触发】检测到地锁 '710001' 关锁成功，开始处理订单...
log
[ 2025-08-11T10:38:58+08:00 ][ error ] 收到消息：dz/pi/setack/710001
error
[ 2025-08-11T10:38:58+08:00 ][ error ] array (
  'topic' => 'dz/pi/setack/710001',
  'message' => '{"VER":"0","CMD":"17","CD":"710001","CL":"1","PACKID":12843,"STATUS":"81"}',
  'timestamp' => '2025-08-11 10:38:58',
)
error
[ 2025-08-11T10:38:58+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T10:38:58+08:00 ][ error ] array (
  'VER' => '0',
  'CMD' => '17',
  'CD' => '710001',
  'CL' => '1',
  'PACKID' => 12843,
  'STATUS' => '81',
)
error
[ 2025-08-11T10:38:58+08:00 ][ error ] 收到命令响应消息，待处理: dz/pi/setack/710001
error
[ 2025-08-11T10:38:58+08:00 ][ log ] 【命令响应】收到针对命令包 '12843' 的ACK确认
log
[ 2025-08-11T10:38:58+08:00 ][ log ] 【命令响应】成功更新命令 '12843' 的状态为 'acked'
log
[ 2025-08-11T10:39:12+08:00 ][ error ] 收到消息：dz/pi/getstatus/710001
error
[ 2025-08-11T10:39:12+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710001',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710001","SIMID":"89861118167312012425","CS":"0","LS":"0","SS":"0","BS":"11.3","RSSI":23,"MT":"4","NG":"0"}',
  'timestamp' => '2025-08-11 10:39:12',
)
error
[ 2025-08-11T10:39:12+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T10:39:12+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710001',
  'SIMID' => '89861118167312012425',
  'CS' => '0',
  'LS' => '0',
  'SS' => '0',
  'BS' => '11.3',
  'RSSI' => 23,
  'MT' => '4',
  'NG' => '0',
)
error
[ 2025-08-11T10:39:12+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710001
error
[ 2025-08-11T10:39:12+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-11T10:39:12+08:00 ][ log ] 【状态上报】开始处理地锁 '710001' 的通用状态...
log
[ 2025-08-11T10:39:12+08:00 ][ log ] 【状态上报】成功更新地锁 '710001' 的快照状态到数据库
log
[ 2025-08-11T10:39:12+08:00 ][ log ] 【状态上报】成功记录地锁 '710001' 的状态到日志表
log
[ 2025-08-11T10:39:12+08:00 ][ log ] 【业务触发】检查 '710001' 的状态变化...
log
[ 2025-08-11T10:39:12+08:00 ][ log ] 【业务触发】检测到地锁 '710001' 开锁成功，结束处理订单...
log
[ 2025-08-11T10:39:12+08:00 ][ log ] 打印order
log
[ 2025-08-11T10:39:12+08:00 ][ log ] array (
  'id' => 47,
  'platform_id' => 20,
  'agent_id' => 82,
  'hospital_id' => 48,
  'hospital_fcbl' => 20.0,
  'hospital_price' => '1.00',
  'hospital_hourlong' => 1,
  'hospital_freedt' => 99999,
  'departments_id' => 34,
  'equipment_id' => 2,
  'equipment_info_id' => 0,
  'info' => '总平台-深圳代理商-南山物业-物业北车棚-设备710001-710001',
  'sn' => 'ord2025081110385395971256',
  'user_id' => 5611,
  'money' => '0.00',
  'status' => '1',
  'pay_types' => '0',
  'pay_status' => '0',
  'pay_time' => NULL,
  'createtime' => 1754879938,
  'returntime' => NULL,
  'updatetime' => 1754879933,
  'is_branch' => '1',
  'admin_info' => NULL,
  'timelong' => 0,
  'timelong_fenzhong' => 0,
  'really_money' => '0.00',
  'actreturntime' => NULL,
  'answer_return_time' => 1754924400,
  'overtime' => NULL,
  'overtime_money' => NULL,
  'normal_money' => NULL,
  'charging_rule' => 1,
  'use_status' => 2,
  'nb_goodsid' => '',
)
log
[ 2025-08-11T10:39:12+08:00 ][ error ] 【业务触发】订单(SN:ord2025081110385395971256)结束逻辑处理成功
error
[ 2025-08-11T10:39:12+08:00 ][ error ] 收到消息：dz/pi/setack/710001
error
[ 2025-08-11T10:39:12+08:00 ][ error ] array (
  'topic' => 'dz/pi/setack/710001',
  'message' => '{"VER":"0","CMD":"17","CD":"710001","CL":"0","PACKID":3686,"STATUS":"1"}',
  'timestamp' => '2025-08-11 10:39:12',
)
error
[ 2025-08-11T10:39:12+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T10:39:12+08:00 ][ error ] array (
  'VER' => '0',
  'CMD' => '17',
  'CD' => '710001',
  'CL' => '0',
  'PACKID' => 3686,
  'STATUS' => '1',
)
error
[ 2025-08-11T10:39:12+08:00 ][ error ] 收到命令响应消息，待处理: dz/pi/setack/710001
error
[ 2025-08-11T10:39:12+08:00 ][ log ] 【命令响应】收到针对命令包 '3686' 的ACK确认
log
[ 2025-08-11T10:39:12+08:00 ][ log ] 【命令响应】成功更新命令 '3686' 的状态为 'acked'
log
[ 2025-08-11T10:41:27+08:00 ][ error ] 收到消息：dz/pi/setack/710001
error
[ 2025-08-11T10:41:27+08:00 ][ error ] array (
  'topic' => 'dz/pi/setack/710001',
  'message' => '{"VER":"0","CMD":"17","CD":"710001","CL":"0","PACKID":4298,"STATUS":"1"}',
  'timestamp' => '2025-08-11 10:41:27',
)
error
[ 2025-08-11T10:41:27+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T10:41:27+08:00 ][ error ] array (
  'VER' => '0',
  'CMD' => '17',
  'CD' => '710001',
  'CL' => '0',
  'PACKID' => 4298,
  'STATUS' => '1',
)
error
[ 2025-08-11T10:41:27+08:00 ][ error ] 收到命令响应消息，待处理: dz/pi/setack/710001
error
[ 2025-08-11T10:41:27+08:00 ][ log ] 【命令响应】收到针对命令包 '4298' 的ACK确认
log
[ 2025-08-11T10:41:27+08:00 ][ log ] 【命令响应】成功更新命令 '4298' 的状态为 'acked'
log
[ 2025-08-11T10:42:32+08:00 ][ error ] 收到消息：dz/pi/getstatus/710001
error
[ 2025-08-11T10:42:32+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710001',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710001","SIMID":"89861118167312012425","CS":"0","LS":"1","SS":"0","BS":"10.9","RSSI":23,"MT":"4","NG":"0"}',
  'timestamp' => '2025-08-11 10:42:32',
)
error
[ 2025-08-11T10:42:32+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T10:42:32+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710001',
  'SIMID' => '89861118167312012425',
  'CS' => '0',
  'LS' => '1',
  'SS' => '0',
  'BS' => '10.9',
  'RSSI' => 23,
  'MT' => '4',
  'NG' => '0',
)
error
[ 2025-08-11T10:42:32+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710001
error
[ 2025-08-11T10:42:32+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-11T10:42:32+08:00 ][ log ] 【状态上报】开始处理地锁 '710001' 的通用状态...
log
[ 2025-08-11T10:42:32+08:00 ][ log ] 【状态上报】成功更新地锁 '710001' 的快照状态到数据库
log
[ 2025-08-11T10:42:32+08:00 ][ log ] 【状态上报】成功记录地锁 '710001' 的状态到日志表
log
[ 2025-08-11T10:42:32+08:00 ][ log ] 【业务触发】检查 '710001' 的状态变化...
log
[ 2025-08-11T10:42:32+08:00 ][ log ] 【业务触发】检测到地锁 '710001' 关锁成功，开始处理订单...
log
[ 2025-08-11T10:42:32+08:00 ][ error ] 收到消息：dz/pi/setack/710001
error
[ 2025-08-11T10:42:32+08:00 ][ error ] array (
  'topic' => 'dz/pi/setack/710001',
  'message' => '{"VER":"0","CMD":"17","CD":"710001","CL":"1","PACKID":53635,"STATUS":"81"}',
  'timestamp' => '2025-08-11 10:42:32',
)
error
[ 2025-08-11T10:42:32+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T10:42:32+08:00 ][ error ] array (
  'VER' => '0',
  'CMD' => '17',
  'CD' => '710001',
  'CL' => '1',
  'PACKID' => 53635,
  'STATUS' => '81',
)
error
[ 2025-08-11T10:42:32+08:00 ][ error ] 收到命令响应消息，待处理: dz/pi/setack/710001
error
[ 2025-08-11T10:42:32+08:00 ][ log ] 【命令响应】收到针对命令包 '53635' 的ACK确认
log
[ 2025-08-11T10:42:32+08:00 ][ log ] 【命令响应】成功更新命令 '53635' 的状态为 'acked'
log
[ 2025-08-11T10:43:13+08:00 ][ error ] 收到消息：dz/pi/getstatus/710001
error
[ 2025-08-11T10:43:13+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710001',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710001","SIMID":"89861118167312012425","CS":"0","LS":"0","SS":"0","BS":"11.5","RSSI":23,"MT":"4","NG":"0"}',
  'timestamp' => '2025-08-11 10:43:13',
)
error
[ 2025-08-11T10:43:13+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T10:43:13+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710001',
  'SIMID' => '89861118167312012425',
  'CS' => '0',
  'LS' => '0',
  'SS' => '0',
  'BS' => '11.5',
  'RSSI' => 23,
  'MT' => '4',
  'NG' => '0',
)
error
[ 2025-08-11T10:43:13+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710001
error
[ 2025-08-11T10:43:13+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-11T10:43:13+08:00 ][ log ] 【状态上报】开始处理地锁 '710001' 的通用状态...
log
[ 2025-08-11T10:43:13+08:00 ][ log ] 【状态上报】成功更新地锁 '710001' 的快照状态到数据库
log
[ 2025-08-11T10:43:13+08:00 ][ log ] 【状态上报】成功记录地锁 '710001' 的状态到日志表
log
[ 2025-08-11T10:43:13+08:00 ][ log ] 【业务触发】检查 '710001' 的状态变化...
log
[ 2025-08-11T10:43:13+08:00 ][ log ] 【业务触发】检测到地锁 '710001' 开锁成功，结束处理订单...
log
[ 2025-08-11T10:43:13+08:00 ][ log ] 打印order
log
[ 2025-08-11T10:43:13+08:00 ][ log ] array (
  'id' => 48,
  'platform_id' => 20,
  'agent_id' => 82,
  'hospital_id' => 48,
  'hospital_fcbl' => 20.0,
  'hospital_price' => '0.00',
  'hospital_hourlong' => 1,
  'hospital_freedt' => 0,
  'departments_id' => 34,
  'equipment_id' => 2,
  'equipment_info_id' => 0,
  'info' => '总平台-深圳代理商-南山物业-物业北车棚-设备710001-710001',
  'sn' => 'ord2025081110422812384467',
  'user_id' => 5611,
  'money' => '0.00',
  'status' => '1',
  'pay_types' => '0',
  'pay_status' => '0',
  'pay_time' => NULL,
  'createtime' => 1754880152,
  'returntime' => NULL,
  'updatetime' => 1754880148,
  'is_branch' => '1',
  'admin_info' => NULL,
  'timelong' => 0,
  'timelong_fenzhong' => 0,
  'really_money' => '0.00',
  'actreturntime' => NULL,
  'answer_return_time' => 1754924400,
  'overtime' => NULL,
  'overtime_money' => NULL,
  'normal_money' => NULL,
  'charging_rule' => 1,
  'use_status' => 2,
  'nb_goodsid' => '',
)
log
[ 2025-08-11T10:43:13+08:00 ][ error ] 【业务触发】订单(SN:ord2025081110422812384467)结束逻辑处理成功
error
[ 2025-08-11T10:43:13+08:00 ][ error ] 收到消息：dz/pi/setack/710001
error
[ 2025-08-11T10:43:13+08:00 ][ error ] array (
  'topic' => 'dz/pi/setack/710001',
  'message' => '{"VER":"0","CMD":"17","CD":"710001","CL":"0","PACKID":12623,"STATUS":"1"}',
  'timestamp' => '2025-08-11 10:43:13',
)
error
[ 2025-08-11T10:43:13+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T10:43:13+08:00 ][ error ] array (
  'VER' => '0',
  'CMD' => '17',
  'CD' => '710001',
  'CL' => '0',
  'PACKID' => 12623,
  'STATUS' => '1',
)
error
[ 2025-08-11T10:43:13+08:00 ][ error ] 收到命令响应消息，待处理: dz/pi/setack/710001
error
[ 2025-08-11T10:43:13+08:00 ][ log ] 【命令响应】收到针对命令包 '12623' 的ACK确认
log
[ 2025-08-11T10:43:13+08:00 ][ log ] 【命令响应】成功更新命令 '12623' 的状态为 'acked'
log
[ 2025-08-11T10:47:14+08:00 ][ error ] 收到消息：dz/pi/getstatus/710001
error
[ 2025-08-11T10:47:14+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710001',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710001","SIMID":"89861118167312012425","CS":"0","LS":"1","SS":"0","BS":"10.2","RSSI":23,"MT":"4","NG":"0"}',
  'timestamp' => '2025-08-11 10:47:14',
)
error
[ 2025-08-11T10:47:14+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T10:47:14+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710001',
  'SIMID' => '89861118167312012425',
  'CS' => '0',
  'LS' => '1',
  'SS' => '0',
  'BS' => '10.2',
  'RSSI' => 23,
  'MT' => '4',
  'NG' => '0',
)
error
[ 2025-08-11T10:47:14+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710001
error
[ 2025-08-11T10:47:14+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-11T10:47:14+08:00 ][ log ] 【状态上报】开始处理地锁 '710001' 的通用状态...
log
[ 2025-08-11T10:47:14+08:00 ][ log ] 【状态上报】成功更新地锁 '710001' 的快照状态到数据库
log
[ 2025-08-11T10:47:14+08:00 ][ log ] 【状态上报】成功记录地锁 '710001' 的状态到日志表
log
[ 2025-08-11T10:47:14+08:00 ][ log ] 【业务触发】检查 '710001' 的状态变化...
log
[ 2025-08-11T10:47:14+08:00 ][ log ] 【业务触发】检测到地锁 '710001' 关锁成功，开始处理订单...
log
[ 2025-08-11T10:47:14+08:00 ][ error ] 收到消息：dz/pi/setack/710001
error
[ 2025-08-11T10:47:14+08:00 ][ error ] array (
  'topic' => 'dz/pi/setack/710001',
  'message' => '{"VER":"0","CMD":"17","CD":"710001","CL":"1","PACKID":56000,"STATUS":"81"}',
  'timestamp' => '2025-08-11 10:47:14',
)
error
[ 2025-08-11T10:47:14+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T10:47:14+08:00 ][ error ] array (
  'VER' => '0',
  'CMD' => '17',
  'CD' => '710001',
  'CL' => '1',
  'PACKID' => 56000,
  'STATUS' => '81',
)
error
[ 2025-08-11T10:47:14+08:00 ][ error ] 收到命令响应消息，待处理: dz/pi/setack/710001
error
[ 2025-08-11T10:47:14+08:00 ][ log ] 【命令响应】收到针对命令包 '56000' 的ACK确认
log
[ 2025-08-11T10:47:14+08:00 ][ log ] 【命令响应】成功更新命令 '56000' 的状态为 'acked'
log
[ 2025-08-11T10:57:14+08:00 ][ error ] 收到消息：dz/pi/getstatus/710001
error
[ 2025-08-11T10:57:14+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710001',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710001","SIMID":"89861118167312012425","CS":"0","LS":"1","SS":"0","BS":"12.3","RSSI":23,"MT":"20","NG":"0"}',
  'timestamp' => '2025-08-11 10:57:14',
)
error
[ 2025-08-11T10:57:14+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T10:57:14+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710001',
  'SIMID' => '89861118167312012425',
  'CS' => '0',
  'LS' => '1',
  'SS' => '0',
  'BS' => '12.3',
  'RSSI' => 23,
  'MT' => '20',
  'NG' => '0',
)
error
[ 2025-08-11T10:57:14+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710001
error
[ 2025-08-11T10:57:14+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-11T10:57:14+08:00 ][ log ] 【状态上报】开始处理地锁 '710001' 的通用状态...
log
[ 2025-08-11T10:57:14+08:00 ][ log ] 【状态上报】成功更新地锁 '710001' 的快照状态到数据库
log
[ 2025-08-11T10:57:14+08:00 ][ log ] 【状态上报】成功记录地锁 '710001' 的状态到日志表
log
[ 2025-08-11T10:57:14+08:00 ][ log ] 【业务触发】检查 '710001' 的状态变化...
log
[ 2025-08-11T10:59:34+08:00 ][ error ] 收到消息：dz/pi/getstatus/710001
error
[ 2025-08-11T10:59:34+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710001',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710001","SIMID":"89861118167312012425","CS":"0","LS":"0","SS":"0","BS":"11.4","RSSI":23,"MT":"4","NG":"0"}',
  'timestamp' => '2025-08-11 10:59:34',
)
error
[ 2025-08-11T10:59:34+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T10:59:34+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710001',
  'SIMID' => '89861118167312012425',
  'CS' => '0',
  'LS' => '0',
  'SS' => '0',
  'BS' => '11.4',
  'RSSI' => 23,
  'MT' => '4',
  'NG' => '0',
)
error
[ 2025-08-11T10:59:34+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710001
error
[ 2025-08-11T10:59:34+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-11T10:59:34+08:00 ][ log ] 【状态上报】开始处理地锁 '710001' 的通用状态...
log
[ 2025-08-11T10:59:34+08:00 ][ log ] 【状态上报】成功更新地锁 '710001' 的快照状态到数据库
log
[ 2025-08-11T10:59:34+08:00 ][ log ] 【状态上报】成功记录地锁 '710001' 的状态到日志表
log
[ 2025-08-11T10:59:34+08:00 ][ log ] 【业务触发】检查 '710001' 的状态变化...
log
[ 2025-08-11T10:59:34+08:00 ][ log ] 【业务触发】检测到地锁 '710001' 开锁成功，结束处理订单...
log
[ 2025-08-11T10:59:34+08:00 ][ log ] 打印order
log
[ 2025-08-11T10:59:34+08:00 ][ log ] array (
  'id' => 49,
  'platform_id' => 20,
  'agent_id' => 82,
  'hospital_id' => 48,
  'hospital_fcbl' => 20.0,
  'hospital_price' => '1.00',
  'hospital_hourlong' => 1,
  'hospital_freedt' => 999999,
  'departments_id' => 34,
  'equipment_id' => 2,
  'equipment_info_id' => 0,
  'info' => '总平台-深圳代理商-南山物业-物业北车棚-设备710001-710001',
  'sn' => 'ord2025081110471037545920',
  'user_id' => 5611,
  'money' => '0.00',
  'status' => '1',
  'pay_types' => '0',
  'pay_status' => '0',
  'pay_time' => NULL,
  'createtime' => 1754880434,
  'returntime' => NULL,
  'updatetime' => 1754880430,
  'is_branch' => '1',
  'admin_info' => NULL,
  'timelong' => 0,
  'timelong_fenzhong' => 0,
  'really_money' => '0.00',
  'actreturntime' => NULL,
  'answer_return_time' => 1754924400,
  'overtime' => NULL,
  'overtime_money' => NULL,
  'normal_money' => NULL,
  'charging_rule' => 1,
  'use_status' => 2,
  'nb_goodsid' => '',
)
log
[ 2025-08-11T10:59:34+08:00 ][ error ] 【业务触发】订单(SN:ord2025081110471037545920)结束逻辑处理成功
error
[ 2025-08-11T10:59:34+08:00 ][ error ] 收到消息：dz/pi/setack/710001
error
[ 2025-08-11T10:59:34+08:00 ][ error ] array (
  'topic' => 'dz/pi/setack/710001',
  'message' => '{"VER":"0","CMD":"17","CD":"710001","CL":"0","PACKID":25411,"STATUS":"1"}',
  'timestamp' => '2025-08-11 10:59:34',
)
error
[ 2025-08-11T10:59:34+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T10:59:34+08:00 ][ error ] array (
  'VER' => '0',
  'CMD' => '17',
  'CD' => '710001',
  'CL' => '0',
  'PACKID' => 25411,
  'STATUS' => '1',
)
error
[ 2025-08-11T10:59:34+08:00 ][ error ] 收到命令响应消息，待处理: dz/pi/setack/710001
error
[ 2025-08-11T10:59:34+08:00 ][ log ] 【命令响应】收到针对命令包 '25411' 的ACK确认
log
[ 2025-08-11T10:59:34+08:00 ][ log ] 【命令响应】成功更新命令 '25411' 的状态为 'acked'
log
[ 2025-08-11T10:59:35+08:00 ][ error ] 收到消息：dz/pi/setack/710001
error
[ 2025-08-11T10:59:35+08:00 ][ error ] array (
  'topic' => 'dz/pi/setack/710001',
  'message' => '{"VER":"0","CMD":"17","CD":"710001","CL":"0","PACKID":25978,"STATUS":"1"}',
  'timestamp' => '2025-08-11 10:59:35',
)
error
[ 2025-08-11T10:59:35+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T10:59:35+08:00 ][ error ] array (
  'VER' => '0',
  'CMD' => '17',
  'CD' => '710001',
  'CL' => '0',
  'PACKID' => 25978,
  'STATUS' => '1',
)
error
[ 2025-08-11T10:59:35+08:00 ][ error ] 收到命令响应消息，待处理: dz/pi/setack/710001
error
[ 2025-08-11T10:59:35+08:00 ][ log ] 【命令响应】收到针对命令包 '25978' 的ACK确认
log
[ 2025-08-11T10:59:35+08:00 ][ log ] 【命令响应】成功更新命令 '25978' 的状态为 'acked'
log
[ 2025-08-11T11:00:11+08:00 ][ error ] 收到消息：dz/pi/getstatus/710001
error
[ 2025-08-11T11:00:11+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710001',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710001","SIMID":"89861118167312012425","CS":"0","LS":"0","SS":"0","BS":"12.2","RSSI":23,"MT":"20","NG":"0"}',
  'timestamp' => '2025-08-11 11:00:11',
)
error
[ 2025-08-11T11:00:11+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T11:00:11+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710001',
  'SIMID' => '89861118167312012425',
  'CS' => '0',
  'LS' => '0',
  'SS' => '0',
  'BS' => '12.2',
  'RSSI' => 23,
  'MT' => '20',
  'NG' => '0',
)
error
[ 2025-08-11T11:00:11+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710001
error
[ 2025-08-11T11:00:11+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-11T11:00:11+08:00 ][ log ] 【状态上报】开始处理地锁 '710001' 的通用状态...
log
[ 2025-08-11T11:00:11+08:00 ][ log ] 【状态上报】成功更新地锁 '710001' 的快照状态到数据库
log
[ 2025-08-11T11:00:11+08:00 ][ log ] 【状态上报】成功记录地锁 '710001' 的状态到日志表
log
[ 2025-08-11T11:00:11+08:00 ][ log ] 【业务触发】检查 '710001' 的状态变化...
log
[ 2025-08-11T11:04:20+08:00 ][ error ] 收到消息：dz/pi/getstatus/710001
error
[ 2025-08-11T11:04:20+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710001',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710001","SIMID":"89861118167312012425","CS":"0","LS":"1","SS":"0","BS":"10.2","RSSI":23,"MT":"4","NG":"0"}',
  'timestamp' => '2025-08-11 11:04:20',
)
error
[ 2025-08-11T11:04:20+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T11:04:20+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710001',
  'SIMID' => '89861118167312012425',
  'CS' => '0',
  'LS' => '1',
  'SS' => '0',
  'BS' => '10.2',
  'RSSI' => 23,
  'MT' => '4',
  'NG' => '0',
)
error
[ 2025-08-11T11:04:20+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710001
error
[ 2025-08-11T11:04:20+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-11T11:04:20+08:00 ][ log ] 【状态上报】开始处理地锁 '710001' 的通用状态...
log
[ 2025-08-11T11:04:20+08:00 ][ log ] 【状态上报】成功更新地锁 '710001' 的快照状态到数据库
log
[ 2025-08-11T11:04:20+08:00 ][ log ] 【状态上报】成功记录地锁 '710001' 的状态到日志表
log
[ 2025-08-11T11:04:20+08:00 ][ log ] 【业务触发】检查 '710001' 的状态变化...
log
[ 2025-08-11T11:04:20+08:00 ][ log ] 【业务触发】检测到地锁 '710001' 关锁成功，开始处理订单...
log
[ 2025-08-11T11:04:20+08:00 ][ error ] 收到消息：dz/pi/setack/710001
error
[ 2025-08-11T11:04:20+08:00 ][ error ] array (
  'topic' => 'dz/pi/setack/710001',
  'message' => '{"VER":"0","CMD":"17","CD":"710001","CL":"1","PACKID":13323,"STATUS":"81"}',
  'timestamp' => '2025-08-11 11:04:20',
)
error
[ 2025-08-11T11:04:20+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T11:04:20+08:00 ][ error ] array (
  'VER' => '0',
  'CMD' => '17',
  'CD' => '710001',
  'CL' => '1',
  'PACKID' => 13323,
  'STATUS' => '81',
)
error
[ 2025-08-11T11:04:20+08:00 ][ error ] 收到命令响应消息，待处理: dz/pi/setack/710001
error
[ 2025-08-11T11:04:20+08:00 ][ log ] 【命令响应】收到针对命令包 '13323' 的ACK确认
log
[ 2025-08-11T11:04:20+08:00 ][ log ] 【命令响应】成功更新命令 '13323' 的状态为 'acked'
log
[ 2025-08-11T11:04:40+08:00 ][ error ] 收到消息：dz/pi/getstatus/710001
error
[ 2025-08-11T11:04:40+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710001',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710001","SIMID":"89861118167312012425","CS":"0","LS":"0","SS":"0","BS":"11.4","RSSI":23,"MT":"4","NG":"0"}',
  'timestamp' => '2025-08-11 11:04:40',
)
error
[ 2025-08-11T11:04:40+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T11:04:40+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710001',
  'SIMID' => '89861118167312012425',
  'CS' => '0',
  'LS' => '0',
  'SS' => '0',
  'BS' => '11.4',
  'RSSI' => 23,
  'MT' => '4',
  'NG' => '0',
)
error
[ 2025-08-11T11:04:40+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710001
error
[ 2025-08-11T11:04:40+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-11T11:04:40+08:00 ][ log ] 【状态上报】开始处理地锁 '710001' 的通用状态...
log
[ 2025-08-11T11:04:40+08:00 ][ log ] 【状态上报】成功更新地锁 '710001' 的快照状态到数据库
log
[ 2025-08-11T11:04:40+08:00 ][ log ] 【状态上报】成功记录地锁 '710001' 的状态到日志表
log
[ 2025-08-11T11:04:40+08:00 ][ log ] 【业务触发】检查 '710001' 的状态变化...
log
[ 2025-08-11T11:04:40+08:00 ][ log ] 【业务触发】检测到地锁 '710001' 开锁成功，结束处理订单...
log
[ 2025-08-11T11:04:40+08:00 ][ log ] 打印order
log
[ 2025-08-11T11:04:40+08:00 ][ log ] array (
  'id' => 50,
  'platform_id' => 20,
  'agent_id' => 82,
  'hospital_id' => 48,
  'hospital_fcbl' => 20.0,
  'hospital_price' => '1.00',
  'hospital_hourlong' => 1,
  'hospital_freedt' => 999999,
  'departments_id' => 34,
  'equipment_id' => 2,
  'equipment_info_id' => 0,
  'info' => '总平台-深圳代理商-南山物业-物业北车棚-设备710001-710001',
  'sn' => 'ord2025081111041649264606',
  'user_id' => 5611,
  'money' => '0.00',
  'status' => '1',
  'pay_types' => '0',
  'pay_status' => '0',
  'pay_time' => NULL,
  'createtime' => 1754881460,
  'returntime' => NULL,
  'updatetime' => 1754881456,
  'is_branch' => '1',
  'admin_info' => NULL,
  'timelong' => 0,
  'timelong_fenzhong' => 0,
  'really_money' => '0.00',
  'actreturntime' => NULL,
  'answer_return_time' => 1754924400,
  'overtime' => NULL,
  'overtime_money' => NULL,
  'normal_money' => NULL,
  'charging_rule' => 1,
  'use_status' => 2,
  'nb_goodsid' => '',
)
log
[ 2025-08-11T11:04:40+08:00 ][ error ] 【业务触发】订单(SN:ord2025081111041649264606)结束逻辑处理成功
error
[ 2025-08-11T11:04:40+08:00 ][ error ] 收到消息：dz/pi/setack/710001
error
[ 2025-08-11T11:04:40+08:00 ][ error ] array (
  'topic' => 'dz/pi/setack/710001',
  'message' => '{"VER":"0","CMD":"17","CD":"710001","CL":"0","PACKID":50756,"STATUS":"1"}',
  'timestamp' => '2025-08-11 11:04:40',
)
error
[ 2025-08-11T11:04:40+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T11:04:40+08:00 ][ error ] array (
  'VER' => '0',
  'CMD' => '17',
  'CD' => '710001',
  'CL' => '0',
  'PACKID' => 50756,
  'STATUS' => '1',
)
error
[ 2025-08-11T11:04:40+08:00 ][ error ] 收到命令响应消息，待处理: dz/pi/setack/710001
error
[ 2025-08-11T11:04:40+08:00 ][ log ] 【命令响应】收到针对命令包 '50756' 的ACK确认
log
[ 2025-08-11T11:04:40+08:00 ][ log ] 【命令响应】成功更新命令 '50756' 的状态为 'acked'
log
[ 2025-08-11T11:05:54+08:00 ][ error ] 收到消息：dz/pi/getstatus/710001
error
[ 2025-08-11T11:05:54+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710001',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710001","SIMID":"89861118167312012425","CS":"0","LS":"1","SS":"0","BS":"10.1","RSSI":23,"MT":"4","NG":"0"}',
  'timestamp' => '2025-08-11 11:05:54',
)
error
[ 2025-08-11T11:05:54+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T11:05:54+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710001',
  'SIMID' => '89861118167312012425',
  'CS' => '0',
  'LS' => '1',
  'SS' => '0',
  'BS' => '10.1',
  'RSSI' => 23,
  'MT' => '4',
  'NG' => '0',
)
error
[ 2025-08-11T11:05:54+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710001
error
[ 2025-08-11T11:05:54+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-11T11:05:54+08:00 ][ log ] 【状态上报】开始处理地锁 '710001' 的通用状态...
log
[ 2025-08-11T11:05:54+08:00 ][ log ] 【状态上报】成功更新地锁 '710001' 的快照状态到数据库
log
[ 2025-08-11T11:05:54+08:00 ][ log ] 【状态上报】成功记录地锁 '710001' 的状态到日志表
log
[ 2025-08-11T11:05:54+08:00 ][ log ] 【业务触发】检查 '710001' 的状态变化...
log
[ 2025-08-11T11:05:54+08:00 ][ log ] 【业务触发】检测到地锁 '710001' 关锁成功，开始处理订单...
log
[ 2025-08-11T11:05:54+08:00 ][ error ] 收到消息：dz/pi/setack/710001
error
[ 2025-08-11T11:05:54+08:00 ][ error ] array (
  'topic' => 'dz/pi/setack/710001',
  'message' => '{"VER":"0","CMD":"17","CD":"710001","CL":"1","PACKID":10935,"STATUS":"81"}',
  'timestamp' => '2025-08-11 11:05:54',
)
error
[ 2025-08-11T11:05:54+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T11:05:54+08:00 ][ error ] array (
  'VER' => '0',
  'CMD' => '17',
  'CD' => '710001',
  'CL' => '1',
  'PACKID' => 10935,
  'STATUS' => '81',
)
error
[ 2025-08-11T11:05:54+08:00 ][ error ] 收到命令响应消息，待处理: dz/pi/setack/710001
error
[ 2025-08-11T11:05:54+08:00 ][ log ] 【命令响应】收到针对命令包 '10935' 的ACK确认
log
[ 2025-08-11T11:05:54+08:00 ][ log ] 【命令响应】成功更新命令 '10935' 的状态为 'acked'
log
[ 2025-08-11T11:07:07+08:00 ][ error ] 收到消息：dz/pi/getstatus/710001
error
[ 2025-08-11T11:07:07+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710001',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710001","SIMID":"89861118167312012425","CS":"0","LS":"0","SS":"0","BS":"11.4","RSSI":23,"MT":"4","NG":"0"}',
  'timestamp' => '2025-08-11 11:07:07',
)
error
[ 2025-08-11T11:07:07+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T11:07:07+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710001',
  'SIMID' => '89861118167312012425',
  'CS' => '0',
  'LS' => '0',
  'SS' => '0',
  'BS' => '11.4',
  'RSSI' => 23,
  'MT' => '4',
  'NG' => '0',
)
error
[ 2025-08-11T11:07:07+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710001
error
[ 2025-08-11T11:07:07+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-11T11:07:07+08:00 ][ log ] 【状态上报】开始处理地锁 '710001' 的通用状态...
log
[ 2025-08-11T11:07:07+08:00 ][ log ] 【状态上报】成功更新地锁 '710001' 的快照状态到数据库
log
[ 2025-08-11T11:07:07+08:00 ][ log ] 【状态上报】成功记录地锁 '710001' 的状态到日志表
log
[ 2025-08-11T11:07:07+08:00 ][ log ] 【业务触发】检查 '710001' 的状态变化...
log
[ 2025-08-11T11:07:07+08:00 ][ log ] 【业务触发】检测到地锁 '710001' 开锁成功，结束处理订单...
log
[ 2025-08-11T11:07:07+08:00 ][ log ] 打印order
log
[ 2025-08-11T11:07:07+08:00 ][ log ] array (
  'id' => 51,
  'platform_id' => 20,
  'agent_id' => 82,
  'hospital_id' => 48,
  'hospital_fcbl' => 20.0,
  'hospital_price' => '1.00',
  'hospital_hourlong' => 1,
  'hospital_freedt' => 999999,
  'departments_id' => 34,
  'equipment_id' => 2,
  'equipment_info_id' => 0,
  'info' => '总平台-深圳代理商-南山物业-物业北车棚-设备710001-710001',
  'sn' => 'ord2025081111054951853207',
  'user_id' => 5611,
  'money' => '0.00',
  'status' => '1',
  'pay_types' => '0',
  'pay_status' => '0',
  'pay_time' => NULL,
  'createtime' => 1754881554,
  'returntime' => NULL,
  'updatetime' => 1754881549,
  'is_branch' => '1',
  'admin_info' => NULL,
  'timelong' => 0,
  'timelong_fenzhong' => 0,
  'really_money' => '0.00',
  'actreturntime' => NULL,
  'answer_return_time' => 1754924400,
  'overtime' => NULL,
  'overtime_money' => NULL,
  'normal_money' => NULL,
  'charging_rule' => 1,
  'use_status' => 2,
  'nb_goodsid' => '',
)
log
[ 2025-08-11T11:07:07+08:00 ][ error ] 【业务触发】订单(SN:ord2025081111054951853207)结束逻辑处理成功
error
[ 2025-08-11T11:07:08+08:00 ][ error ] 收到消息：dz/pi/setack/710001
error
[ 2025-08-11T11:07:08+08:00 ][ error ] array (
  'topic' => 'dz/pi/setack/710001',
  'message' => '{"VER":"0","CMD":"17","CD":"710001","CL":"0","PACKID":33212,"STATUS":"1"}',
  'timestamp' => '2025-08-11 11:07:08',
)
error
[ 2025-08-11T11:07:08+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T11:07:08+08:00 ][ error ] array (
  'VER' => '0',
  'CMD' => '17',
  'CD' => '710001',
  'CL' => '0',
  'PACKID' => 33212,
  'STATUS' => '1',
)
error
[ 2025-08-11T11:07:08+08:00 ][ error ] 收到命令响应消息，待处理: dz/pi/setack/710001
error
[ 2025-08-11T11:07:08+08:00 ][ log ] 【命令响应】收到针对命令包 '33212' 的ACK确认
log
[ 2025-08-11T11:07:08+08:00 ][ log ] 【命令响应】成功更新命令 '33212' 的状态为 'acked'
log
[ 2025-08-11T11:07:22+08:00 ][ error ] 收到消息：dz/pi/getstatus/710001
error
[ 2025-08-11T11:07:22+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710001',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710001","SIMID":"89861118167312012425","CS":"0","LS":"1","SS":"0","BS":"10.9","RSSI":23,"MT":"4","NG":"0"}',
  'timestamp' => '2025-08-11 11:07:22',
)
error
[ 2025-08-11T11:07:22+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T11:07:22+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710001',
  'SIMID' => '89861118167312012425',
  'CS' => '0',
  'LS' => '1',
  'SS' => '0',
  'BS' => '10.9',
  'RSSI' => 23,
  'MT' => '4',
  'NG' => '0',
)
error
[ 2025-08-11T11:07:22+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710001
error
[ 2025-08-11T11:07:22+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-11T11:07:22+08:00 ][ log ] 【状态上报】开始处理地锁 '710001' 的通用状态...
log
[ 2025-08-11T11:07:22+08:00 ][ log ] 【状态上报】成功更新地锁 '710001' 的快照状态到数据库
log
[ 2025-08-11T11:07:22+08:00 ][ log ] 【状态上报】成功记录地锁 '710001' 的状态到日志表
log
[ 2025-08-11T11:07:22+08:00 ][ log ] 【业务触发】检查 '710001' 的状态变化...
log
[ 2025-08-11T11:07:22+08:00 ][ log ] 【业务触发】检测到地锁 '710001' 关锁成功，开始处理订单...
log
[ 2025-08-11T11:07:22+08:00 ][ error ] 收到消息：dz/pi/setack/710001
error
[ 2025-08-11T11:07:22+08:00 ][ error ] array (
  'topic' => 'dz/pi/setack/710001',
  'message' => '{"VER":"0","CMD":"17","CD":"710001","CL":"1","PACKID":49055,"STATUS":"81"}',
  'timestamp' => '2025-08-11 11:07:22',
)
error
[ 2025-08-11T11:07:22+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T11:07:22+08:00 ][ error ] array (
  'VER' => '0',
  'CMD' => '17',
  'CD' => '710001',
  'CL' => '1',
  'PACKID' => 49055,
  'STATUS' => '81',
)
error
[ 2025-08-11T11:07:22+08:00 ][ error ] 收到命令响应消息，待处理: dz/pi/setack/710001
error
[ 2025-08-11T11:07:22+08:00 ][ log ] 【命令响应】收到针对命令包 '49055' 的ACK确认
log
[ 2025-08-11T11:07:22+08:00 ][ log ] 【命令响应】成功更新命令 '49055' 的状态为 'acked'
log
[ 2025-08-11T11:07:36+08:00 ][ error ] 收到消息：dz/pi/getstatus/710001
error
[ 2025-08-11T11:07:36+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710001',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710001","SIMID":"89861118167312012425","CS":"0","LS":"0","SS":"0","BS":"11.4","RSSI":23,"MT":"4","NG":"0"}',
  'timestamp' => '2025-08-11 11:07:36',
)
error
[ 2025-08-11T11:07:36+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T11:07:36+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710001',
  'SIMID' => '89861118167312012425',
  'CS' => '0',
  'LS' => '0',
  'SS' => '0',
  'BS' => '11.4',
  'RSSI' => 23,
  'MT' => '4',
  'NG' => '0',
)
error
[ 2025-08-11T11:07:36+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710001
error
[ 2025-08-11T11:07:36+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-11T11:07:36+08:00 ][ log ] 【状态上报】开始处理地锁 '710001' 的通用状态...
log
[ 2025-08-11T11:07:36+08:00 ][ log ] 【状态上报】成功更新地锁 '710001' 的快照状态到数据库
log
[ 2025-08-11T11:07:36+08:00 ][ log ] 【状态上报】成功记录地锁 '710001' 的状态到日志表
log
[ 2025-08-11T11:07:36+08:00 ][ log ] 【业务触发】检查 '710001' 的状态变化...
log
[ 2025-08-11T11:07:36+08:00 ][ log ] 【业务触发】检测到地锁 '710001' 开锁成功，结束处理订单...
log
[ 2025-08-11T11:07:36+08:00 ][ log ] 打印order
log
[ 2025-08-11T11:07:36+08:00 ][ log ] array (
  'id' => 52,
  'platform_id' => 20,
  'agent_id' => 82,
  'hospital_id' => 48,
  'hospital_fcbl' => 20.0,
  'hospital_price' => '1.00',
  'hospital_hourlong' => 1,
  'hospital_freedt' => 999999,
  'departments_id' => 34,
  'equipment_id' => 2,
  'equipment_info_id' => 0,
  'info' => '总平台-深圳代理商-南山物业-物业北车棚-设备710001-710001',
  'sn' => 'ord2025081111071890130496',
  'user_id' => 5611,
  'money' => '0.00',
  'status' => '1',
  'pay_types' => '0',
  'pay_status' => '0',
  'pay_time' => NULL,
  'createtime' => 1754881642,
  'returntime' => NULL,
  'updatetime' => 1754881638,
  'is_branch' => '1',
  'admin_info' => NULL,
  'timelong' => 0,
  'timelong_fenzhong' => 0,
  'really_money' => '0.00',
  'actreturntime' => NULL,
  'answer_return_time' => 1754924400,
  'overtime' => NULL,
  'overtime_money' => NULL,
  'normal_money' => NULL,
  'charging_rule' => 1,
  'use_status' => 2,
  'nb_goodsid' => '',
)
log
[ 2025-08-11T11:07:36+08:00 ][ error ] 【业务触发】订单(SN:ord2025081111071890130496)结束逻辑处理成功
error
[ 2025-08-11T11:07:36+08:00 ][ error ] 收到消息：dz/pi/setack/710001
error
[ 2025-08-11T11:07:36+08:00 ][ error ] array (
  'topic' => 'dz/pi/setack/710001',
  'message' => '{"VER":"0","CMD":"17","CD":"710001","CL":"0","PACKID":53143,"STATUS":"1"}',
  'timestamp' => '2025-08-11 11:07:36',
)
error
[ 2025-08-11T11:07:36+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T11:07:36+08:00 ][ error ] array (
  'VER' => '0',
  'CMD' => '17',
  'CD' => '710001',
  'CL' => '0',
  'PACKID' => 53143,
  'STATUS' => '1',
)
error
[ 2025-08-11T11:07:36+08:00 ][ error ] 收到命令响应消息，待处理: dz/pi/setack/710001
error
[ 2025-08-11T11:07:36+08:00 ][ log ] 【命令响应】收到针对命令包 '53143' 的ACK确认
log
[ 2025-08-11T11:07:36+08:00 ][ log ] 【命令响应】成功更新命令 '53143' 的状态为 'acked'
log
[ 2025-08-11T11:17:35+08:00 ][ error ] 收到消息：dz/pi/getstatus/710001
error
[ 2025-08-11T11:17:35+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710001',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710001","SIMID":"89861118167312012425","CS":"0","LS":"0","SS":"0","BS":"12.3","RSSI":23,"MT":"20","NG":"0"}',
  'timestamp' => '2025-08-11 11:17:35',
)
error
[ 2025-08-11T11:17:35+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T11:17:35+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710001',
  'SIMID' => '89861118167312012425',
  'CS' => '0',
  'LS' => '0',
  'SS' => '0',
  'BS' => '12.3',
  'RSSI' => 23,
  'MT' => '20',
  'NG' => '0',
)
error
[ 2025-08-11T11:17:35+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710001
error
[ 2025-08-11T11:17:35+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-11T11:17:35+08:00 ][ log ] 【状态上报】开始处理地锁 '710001' 的通用状态...
log
[ 2025-08-11T11:17:35+08:00 ][ log ] 【状态上报】成功更新地锁 '710001' 的快照状态到数据库
log
[ 2025-08-11T11:17:35+08:00 ][ log ] 【状态上报】成功记录地锁 '710001' 的状态到日志表
log
[ 2025-08-11T11:17:35+08:00 ][ log ] 【业务触发】检查 '710001' 的状态变化...
log
[ 2025-08-11T11:27:35+08:00 ][ error ] 收到消息：dz/pi/getstatus/710001
error
[ 2025-08-11T11:27:35+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710001',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710001","SIMID":"89861118167312012425","CS":"0","LS":"0","SS":"0","BS":"12.3","RSSI":23,"MT":"20","NG":"0"}',
  'timestamp' => '2025-08-11 11:27:35',
)
error
[ 2025-08-11T11:27:35+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T11:27:35+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710001',
  'SIMID' => '89861118167312012425',
  'CS' => '0',
  'LS' => '0',
  'SS' => '0',
  'BS' => '12.3',
  'RSSI' => 23,
  'MT' => '20',
  'NG' => '0',
)
error
[ 2025-08-11T11:27:35+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710001
error
[ 2025-08-11T11:27:35+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-11T11:27:35+08:00 ][ log ] 【状态上报】开始处理地锁 '710001' 的通用状态...
log
[ 2025-08-11T11:27:35+08:00 ][ log ] 【状态上报】成功更新地锁 '710001' 的快照状态到数据库
log
[ 2025-08-11T11:27:35+08:00 ][ log ] 【状态上报】成功记录地锁 '710001' 的状态到日志表
log
[ 2025-08-11T11:27:35+08:00 ][ log ] 【业务触发】检查 '710001' 的状态变化...
log
[ 2025-08-11T11:37:34+08:00 ][ error ] 收到消息：dz/pi/getstatus/710001
error
[ 2025-08-11T11:37:34+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710001',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710001","SIMID":"89861118167312012425","CS":"0","LS":"0","SS":"0","BS":"12.3","RSSI":23,"MT":"20","NG":"0"}',
  'timestamp' => '2025-08-11 11:37:34',
)
error
[ 2025-08-11T11:37:34+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T11:37:34+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710001',
  'SIMID' => '89861118167312012425',
  'CS' => '0',
  'LS' => '0',
  'SS' => '0',
  'BS' => '12.3',
  'RSSI' => 23,
  'MT' => '20',
  'NG' => '0',
)
error
[ 2025-08-11T11:37:34+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710001
error
[ 2025-08-11T11:37:34+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-11T11:37:34+08:00 ][ log ] 【状态上报】开始处理地锁 '710001' 的通用状态...
log
[ 2025-08-11T11:37:34+08:00 ][ log ] 【状态上报】成功更新地锁 '710001' 的快照状态到数据库
log
[ 2025-08-11T11:37:34+08:00 ][ log ] 【状态上报】成功记录地锁 '710001' 的状态到日志表
log
[ 2025-08-11T11:37:34+08:00 ][ log ] 【业务触发】检查 '710001' 的状态变化...
log
[ 2025-08-11T11:47:33+08:00 ][ error ] 收到消息：dz/pi/getstatus/710001
error
[ 2025-08-11T11:47:33+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710001',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710001","SIMID":"89861118167312012425","CS":"0","LS":"0","SS":"0","BS":"12.3","RSSI":23,"MT":"20","NG":"0"}',
  'timestamp' => '2025-08-11 11:47:33',
)
error
[ 2025-08-11T11:47:33+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T11:47:33+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710001',
  'SIMID' => '89861118167312012425',
  'CS' => '0',
  'LS' => '0',
  'SS' => '0',
  'BS' => '12.3',
  'RSSI' => 23,
  'MT' => '20',
  'NG' => '0',
)
error
[ 2025-08-11T11:47:33+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710001
error
[ 2025-08-11T11:47:33+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-11T11:47:33+08:00 ][ log ] 【状态上报】开始处理地锁 '710001' 的通用状态...
log
[ 2025-08-11T11:47:33+08:00 ][ log ] 【状态上报】成功更新地锁 '710001' 的快照状态到数据库
log
[ 2025-08-11T11:47:33+08:00 ][ log ] 【状态上报】成功记录地锁 '710001' 的状态到日志表
log
[ 2025-08-11T11:47:33+08:00 ][ log ] 【业务触发】检查 '710001' 的状态变化...
log
[ 2025-08-11T11:57:33+08:00 ][ error ] 收到消息：dz/pi/getstatus/710001
error
[ 2025-08-11T11:57:33+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710001',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710001","SIMID":"89861118167312012425","CS":"0","LS":"0","SS":"0","BS":"12.3","RSSI":23,"MT":"20","NG":"0"}',
  'timestamp' => '2025-08-11 11:57:33',
)
error
[ 2025-08-11T11:57:33+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T11:57:33+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710001',
  'SIMID' => '89861118167312012425',
  'CS' => '0',
  'LS' => '0',
  'SS' => '0',
  'BS' => '12.3',
  'RSSI' => 23,
  'MT' => '20',
  'NG' => '0',
)
error
[ 2025-08-11T11:57:33+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710001
error
[ 2025-08-11T11:57:33+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-11T11:57:33+08:00 ][ log ] 【状态上报】开始处理地锁 '710001' 的通用状态...
log
[ 2025-08-11T11:57:33+08:00 ][ log ] 【状态上报】成功更新地锁 '710001' 的快照状态到数据库
log
[ 2025-08-11T11:57:33+08:00 ][ log ] 【状态上报】成功记录地锁 '710001' 的状态到日志表
log
[ 2025-08-11T11:57:33+08:00 ][ log ] 【业务触发】检查 '710001' 的状态变化...
log
[ 2025-08-11T12:05:31+08:00 ][ error ] 收到消息：dz/pi/getstatus/710001
error
[ 2025-08-11T12:05:31+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710001',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710001","SIMID":"89861118167312012425","CS":"0","LS":"1","SS":"0","BS":"10.8","RSSI":23,"MT":"4","NG":"0"}',
  'timestamp' => '2025-08-11 12:05:31',
)
error
[ 2025-08-11T12:05:31+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T12:05:31+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710001',
  'SIMID' => '89861118167312012425',
  'CS' => '0',
  'LS' => '1',
  'SS' => '0',
  'BS' => '10.8',
  'RSSI' => 23,
  'MT' => '4',
  'NG' => '0',
)
error
[ 2025-08-11T12:05:31+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710001
error
[ 2025-08-11T12:05:31+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-11T12:05:31+08:00 ][ log ] 【状态上报】开始处理地锁 '710001' 的通用状态...
log
[ 2025-08-11T12:05:31+08:00 ][ log ] 【状态上报】成功更新地锁 '710001' 的快照状态到数据库
log
[ 2025-08-11T12:05:31+08:00 ][ log ] 【状态上报】成功记录地锁 '710001' 的状态到日志表
log
[ 2025-08-11T12:05:31+08:00 ][ log ] 【业务触发】检查 '710001' 的状态变化...
log
[ 2025-08-11T12:05:31+08:00 ][ log ] 【业务触发】检测到地锁 '710001' 关锁成功，开始处理订单...
log
[ 2025-08-11T12:05:31+08:00 ][ error ] 收到消息：dz/pi/setack/710001
error
[ 2025-08-11T12:05:31+08:00 ][ error ] array (
  'topic' => 'dz/pi/setack/710001',
  'message' => '{"VER":"0","CMD":"17","CD":"710001","CL":"1","PACKID":21822,"STATUS":"81"}',
  'timestamp' => '2025-08-11 12:05:31',
)
error
[ 2025-08-11T12:05:31+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T12:05:31+08:00 ][ error ] array (
  'VER' => '0',
  'CMD' => '17',
  'CD' => '710001',
  'CL' => '1',
  'PACKID' => 21822,
  'STATUS' => '81',
)
error
[ 2025-08-11T12:05:31+08:00 ][ error ] 收到命令响应消息，待处理: dz/pi/setack/710001
error
[ 2025-08-11T12:05:31+08:00 ][ log ] 【命令响应】收到针对命令包 '21822' 的ACK确认
log
[ 2025-08-11T12:05:31+08:00 ][ log ] 【命令响应】成功更新命令 '21822' 的状态为 'acked'
log
[ 2025-08-11T12:05:45+08:00 ][ error ] 收到消息：dz/pi/getstatus/710001
error
[ 2025-08-11T12:05:45+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710001',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710001","SIMID":"89861118167312012425","CS":"0","LS":"0","SS":"0","BS":"11.4","RSSI":23,"MT":"4","NG":"0"}',
  'timestamp' => '2025-08-11 12:05:45',
)
error
[ 2025-08-11T12:05:45+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T12:05:45+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710001',
  'SIMID' => '89861118167312012425',
  'CS' => '0',
  'LS' => '0',
  'SS' => '0',
  'BS' => '11.4',
  'RSSI' => 23,
  'MT' => '4',
  'NG' => '0',
)
error
[ 2025-08-11T12:05:45+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710001
error
[ 2025-08-11T12:05:45+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-11T12:05:45+08:00 ][ log ] 【状态上报】开始处理地锁 '710001' 的通用状态...
log
[ 2025-08-11T12:05:45+08:00 ][ log ] 【状态上报】成功更新地锁 '710001' 的快照状态到数据库
log
[ 2025-08-11T12:05:45+08:00 ][ log ] 【状态上报】成功记录地锁 '710001' 的状态到日志表
log
[ 2025-08-11T12:05:45+08:00 ][ log ] 【业务触发】检查 '710001' 的状态变化...
log
[ 2025-08-11T12:05:45+08:00 ][ log ] 【业务触发】检测到地锁 '710001' 开锁成功，结束处理订单...
log
[ 2025-08-11T12:05:45+08:00 ][ log ] 打印order
log
[ 2025-08-11T12:05:45+08:00 ][ log ] array (
  'id' => 53,
  'platform_id' => 20,
  'agent_id' => 82,
  'hospital_id' => 48,
  'hospital_fcbl' => 20.0,
  'hospital_price' => '1.00',
  'hospital_hourlong' => 1,
  'hospital_freedt' => 999999,
  'departments_id' => 34,
  'equipment_id' => 2,
  'equipment_info_id' => 0,
  'info' => '总平台-深圳代理商-南山物业-物业北车棚-设备710001-710001',
  'sn' => 'ord2025081112052790214921',
  'user_id' => 5611,
  'money' => '0.00',
  'status' => '1',
  'pay_types' => '0',
  'pay_status' => '0',
  'pay_time' => NULL,
  'createtime' => 1754885131,
  'returntime' => NULL,
  'updatetime' => 1754885127,
  'is_branch' => '1',
  'admin_info' => NULL,
  'timelong' => 0,
  'timelong_fenzhong' => 0,
  'really_money' => '0.00',
  'actreturntime' => NULL,
  'answer_return_time' => 1754924400,
  'overtime' => NULL,
  'overtime_money' => NULL,
  'normal_money' => NULL,
  'charging_rule' => 1,
  'use_status' => 2,
  'nb_goodsid' => '',
)
log
[ 2025-08-11T12:05:45+08:00 ][ error ] 【业务触发】订单(SN:ord2025081112052790214921)结束逻辑处理成功
error
[ 2025-08-11T12:05:45+08:00 ][ error ] 收到消息：dz/pi/setack/710001
error
[ 2025-08-11T12:05:45+08:00 ][ error ] array (
  'topic' => 'dz/pi/setack/710001',
  'message' => '{"VER":"0","CMD":"17","CD":"710001","CL":"0","PACKID":33715,"STATUS":"1"}',
  'timestamp' => '2025-08-11 12:05:45',
)
error
[ 2025-08-11T12:05:45+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T12:05:45+08:00 ][ error ] array (
  'VER' => '0',
  'CMD' => '17',
  'CD' => '710001',
  'CL' => '0',
  'PACKID' => 33715,
  'STATUS' => '1',
)
error
[ 2025-08-11T12:05:45+08:00 ][ error ] 收到命令响应消息，待处理: dz/pi/setack/710001
error
[ 2025-08-11T12:05:45+08:00 ][ log ] 【命令响应】收到针对命令包 '33715' 的ACK确认
log
[ 2025-08-11T12:05:45+08:00 ][ log ] 【命令响应】成功更新命令 '33715' 的状态为 'acked'
log
[ 2025-08-11T12:06:24+08:00 ][ error ] 收到消息：dz/pi/getstatus/710001
error
[ 2025-08-11T12:06:24+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710001',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710001","SIMID":"89861118167312012425","CS":"0","LS":"1","SS":"0","BS":"10.8","RSSI":23,"MT":"4","NG":"0"}',
  'timestamp' => '2025-08-11 12:06:24',
)
error
[ 2025-08-11T12:06:24+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T12:06:24+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710001',
  'SIMID' => '89861118167312012425',
  'CS' => '0',
  'LS' => '1',
  'SS' => '0',
  'BS' => '10.8',
  'RSSI' => 23,
  'MT' => '4',
  'NG' => '0',
)
error
[ 2025-08-11T12:06:24+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710001
error
[ 2025-08-11T12:06:24+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-11T12:06:24+08:00 ][ log ] 【状态上报】开始处理地锁 '710001' 的通用状态...
log
[ 2025-08-11T12:06:24+08:00 ][ log ] 【状态上报】成功更新地锁 '710001' 的快照状态到数据库
log
[ 2025-08-11T12:06:24+08:00 ][ log ] 【状态上报】成功记录地锁 '710001' 的状态到日志表
log
[ 2025-08-11T12:06:24+08:00 ][ log ] 【业务触发】检查 '710001' 的状态变化...
log
[ 2025-08-11T12:06:24+08:00 ][ log ] 【业务触发】检测到地锁 '710001' 关锁成功，开始处理订单...
log
[ 2025-08-11T12:06:24+08:00 ][ error ] 收到消息：dz/pi/setack/710001
error
[ 2025-08-11T12:06:24+08:00 ][ error ] array (
  'topic' => 'dz/pi/setack/710001',
  'message' => '{"VER":"0","CMD":"17","CD":"710001","CL":"1","PACKID":40010,"STATUS":"81"}',
  'timestamp' => '2025-08-11 12:06:24',
)
error
[ 2025-08-11T12:06:24+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T12:06:24+08:00 ][ error ] array (
  'VER' => '0',
  'CMD' => '17',
  'CD' => '710001',
  'CL' => '1',
  'PACKID' => 40010,
  'STATUS' => '81',
)
error
[ 2025-08-11T12:06:24+08:00 ][ error ] 收到命令响应消息，待处理: dz/pi/setack/710001
error
[ 2025-08-11T12:06:24+08:00 ][ log ] 【命令响应】收到针对命令包 '40010' 的ACK确认
log
[ 2025-08-11T12:06:24+08:00 ][ log ] 【命令响应】成功更新命令 '40010' 的状态为 'acked'
log
[ 2025-08-11T12:06:37+08:00 ][ error ] 收到消息：dz/pi/getstatus/710001
error
[ 2025-08-11T12:06:37+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710001',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710001","SIMID":"89861118167312012425","CS":"0","LS":"0","SS":"0","BS":"11.3","RSSI":23,"MT":"4","NG":"0"}',
  'timestamp' => '2025-08-11 12:06:37',
)
error
[ 2025-08-11T12:06:37+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T12:06:37+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710001',
  'SIMID' => '89861118167312012425',
  'CS' => '0',
  'LS' => '0',
  'SS' => '0',
  'BS' => '11.3',
  'RSSI' => 23,
  'MT' => '4',
  'NG' => '0',
)
error
[ 2025-08-11T12:06:37+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710001
error
[ 2025-08-11T12:06:37+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-11T12:06:37+08:00 ][ log ] 【状态上报】开始处理地锁 '710001' 的通用状态...
log
[ 2025-08-11T12:06:37+08:00 ][ log ] 【状态上报】成功更新地锁 '710001' 的快照状态到数据库
log
[ 2025-08-11T12:06:37+08:00 ][ log ] 【状态上报】成功记录地锁 '710001' 的状态到日志表
log
[ 2025-08-11T12:06:37+08:00 ][ log ] 【业务触发】检查 '710001' 的状态变化...
log
[ 2025-08-11T12:06:37+08:00 ][ log ] 【业务触发】检测到地锁 '710001' 开锁成功，结束处理订单...
log
[ 2025-08-11T12:06:37+08:00 ][ log ] 打印order
log
[ 2025-08-11T12:06:37+08:00 ][ log ] array (
  'id' => 54,
  'platform_id' => 20,
  'agent_id' => 82,
  'hospital_id' => 48,
  'hospital_fcbl' => 20.0,
  'hospital_price' => '1.00',
  'hospital_hourlong' => 1,
  'hospital_freedt' => 999999,
  'departments_id' => 34,
  'equipment_id' => 2,
  'equipment_info_id' => 0,
  'info' => '总平台-深圳代理商-南山物业-物业北车棚-设备710001-710001',
  'sn' => 'ord2025081112062065266165',
  'user_id' => 5611,
  'money' => '0.00',
  'status' => '1',
  'pay_types' => '0',
  'pay_status' => '0',
  'pay_time' => NULL,
  'createtime' => 1754885184,
  'returntime' => NULL,
  'updatetime' => 1754885180,
  'is_branch' => '1',
  'admin_info' => NULL,
  'timelong' => 0,
  'timelong_fenzhong' => 0,
  'really_money' => '0.00',
  'actreturntime' => NULL,
  'answer_return_time' => 1754924400,
  'overtime' => NULL,
  'overtime_money' => NULL,
  'normal_money' => NULL,
  'charging_rule' => 1,
  'use_status' => 2,
  'nb_goodsid' => '',
)
log
[ 2025-08-11T12:06:37+08:00 ][ error ] 【业务触发】订单(SN:ord2025081112062065266165)结束逻辑处理成功
error
[ 2025-08-11T12:06:37+08:00 ][ error ] 收到消息：dz/pi/setack/710001
error
[ 2025-08-11T12:06:37+08:00 ][ error ] array (
  'topic' => 'dz/pi/setack/710001',
  'message' => '{"VER":"0","CMD":"17","CD":"710001","CL":"0","PACKID":611,"STATUS":"1"}',
  'timestamp' => '2025-08-11 12:06:37',
)
error
[ 2025-08-11T12:06:37+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T12:06:37+08:00 ][ error ] array (
  'VER' => '0',
  'CMD' => '17',
  'CD' => '710001',
  'CL' => '0',
  'PACKID' => 611,
  'STATUS' => '1',
)
error
[ 2025-08-11T12:06:37+08:00 ][ error ] 收到命令响应消息，待处理: dz/pi/setack/710001
error
[ 2025-08-11T12:06:37+08:00 ][ log ] 【命令响应】收到针对命令包 '611' 的ACK确认
log
[ 2025-08-11T12:06:37+08:00 ][ log ] 【命令响应】成功更新命令 '611' 的状态为 'acked'
log
[ 2025-08-11T12:07:16+08:00 ][ error ] 收到消息：dz/pi/getstatus/710001
error
[ 2025-08-11T12:07:16+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710001',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710001","SIMID":"89861118167312012425","CS":"0","LS":"1","SS":"0","BS":"10.2","RSSI":23,"MT":"4","NG":"0"}',
  'timestamp' => '2025-08-11 12:07:16',
)
error
[ 2025-08-11T12:07:16+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T12:07:16+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710001',
  'SIMID' => '89861118167312012425',
  'CS' => '0',
  'LS' => '1',
  'SS' => '0',
  'BS' => '10.2',
  'RSSI' => 23,
  'MT' => '4',
  'NG' => '0',
)
error
[ 2025-08-11T12:07:16+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710001
error
[ 2025-08-11T12:07:16+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-11T12:07:16+08:00 ][ log ] 【状态上报】开始处理地锁 '710001' 的通用状态...
log
[ 2025-08-11T12:07:16+08:00 ][ log ] 【状态上报】成功更新地锁 '710001' 的快照状态到数据库
log
[ 2025-08-11T12:07:16+08:00 ][ log ] 【状态上报】成功记录地锁 '710001' 的状态到日志表
log
[ 2025-08-11T12:07:16+08:00 ][ log ] 【业务触发】检查 '710001' 的状态变化...
log
[ 2025-08-11T12:07:16+08:00 ][ log ] 【业务触发】检测到地锁 '710001' 关锁成功，开始处理订单...
log
[ 2025-08-11T12:07:16+08:00 ][ error ] 收到消息：dz/pi/setack/710001
error
[ 2025-08-11T12:07:16+08:00 ][ error ] array (
  'topic' => 'dz/pi/setack/710001',
  'message' => '{"VER":"0","CMD":"17","CD":"710001","CL":"1","PACKID":46715,"STATUS":"81"}',
  'timestamp' => '2025-08-11 12:07:16',
)
error
[ 2025-08-11T12:07:16+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T12:07:16+08:00 ][ error ] array (
  'VER' => '0',
  'CMD' => '17',
  'CD' => '710001',
  'CL' => '1',
  'PACKID' => 46715,
  'STATUS' => '81',
)
error
[ 2025-08-11T12:07:16+08:00 ][ error ] 收到命令响应消息，待处理: dz/pi/setack/710001
error
[ 2025-08-11T12:07:16+08:00 ][ log ] 【命令响应】收到针对命令包 '46715' 的ACK确认
log
[ 2025-08-11T12:07:16+08:00 ][ log ] 【命令响应】成功更新命令 '46715' 的状态为 'acked'
log
[ 2025-08-11T12:07:27+08:00 ][ error ] 收到消息：dz/pi/getstatus/710001
error
[ 2025-08-11T12:07:27+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710001',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710001","SIMID":"89861118167312012425","CS":"0","LS":"0","SS":"0","BS":"11.3","RSSI":23,"MT":"4","NG":"0"}',
  'timestamp' => '2025-08-11 12:07:27',
)
error
[ 2025-08-11T12:07:27+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T12:07:27+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710001',
  'SIMID' => '89861118167312012425',
  'CS' => '0',
  'LS' => '0',
  'SS' => '0',
  'BS' => '11.3',
  'RSSI' => 23,
  'MT' => '4',
  'NG' => '0',
)
error
[ 2025-08-11T12:07:27+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710001
error
[ 2025-08-11T12:07:27+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-11T12:07:27+08:00 ][ log ] 【状态上报】开始处理地锁 '710001' 的通用状态...
log
[ 2025-08-11T12:07:27+08:00 ][ log ] 【状态上报】成功更新地锁 '710001' 的快照状态到数据库
log
[ 2025-08-11T12:07:27+08:00 ][ log ] 【状态上报】成功记录地锁 '710001' 的状态到日志表
log
[ 2025-08-11T12:07:27+08:00 ][ log ] 【业务触发】检查 '710001' 的状态变化...
log
[ 2025-08-11T12:07:27+08:00 ][ log ] 【业务触发】检测到地锁 '710001' 开锁成功，结束处理订单...
log
[ 2025-08-11T12:07:27+08:00 ][ log ] 打印order
log
[ 2025-08-11T12:07:27+08:00 ][ log ] array (
  'id' => 55,
  'platform_id' => 20,
  'agent_id' => 82,
  'hospital_id' => 48,
  'hospital_fcbl' => 20.0,
  'hospital_price' => '1.00',
  'hospital_hourlong' => 1,
  'hospital_freedt' => 999999,
  'departments_id' => 34,
  'equipment_id' => 2,
  'equipment_info_id' => 0,
  'info' => '总平台-深圳代理商-南山物业-物业北车棚-设备710001-710001',
  'sn' => 'ord2025081112071269459417',
  'user_id' => 5611,
  'money' => '0.00',
  'status' => '1',
  'pay_types' => '0',
  'pay_status' => '0',
  'pay_time' => NULL,
  'createtime' => 1754885236,
  'returntime' => NULL,
  'updatetime' => 1754885232,
  'is_branch' => '1',
  'admin_info' => NULL,
  'timelong' => 0,
  'timelong_fenzhong' => 0,
  'really_money' => '0.00',
  'actreturntime' => NULL,
  'answer_return_time' => 1754924400,
  'overtime' => NULL,
  'overtime_money' => NULL,
  'normal_money' => NULL,
  'charging_rule' => 1,
  'use_status' => 2,
  'nb_goodsid' => '',
)
log
[ 2025-08-11T12:07:27+08:00 ][ error ] 【业务触发】订单(SN:ord2025081112071269459417)结束逻辑处理成功
error
[ 2025-08-11T12:07:27+08:00 ][ error ] 收到消息：dz/pi/setack/710001
error
[ 2025-08-11T12:07:27+08:00 ][ error ] array (
  'topic' => 'dz/pi/setack/710001',
  'message' => '{"VER":"0","CMD":"17","CD":"710001","CL":"0","PACKID":56652,"STATUS":"1"}',
  'timestamp' => '2025-08-11 12:07:27',
)
error
[ 2025-08-11T12:07:27+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T12:07:27+08:00 ][ error ] array (
  'VER' => '0',
  'CMD' => '17',
  'CD' => '710001',
  'CL' => '0',
  'PACKID' => 56652,
  'STATUS' => '1',
)
error
[ 2025-08-11T12:07:27+08:00 ][ error ] 收到命令响应消息，待处理: dz/pi/setack/710001
error
[ 2025-08-11T12:07:27+08:00 ][ log ] 【命令响应】收到针对命令包 '56652' 的ACK确认
log
[ 2025-08-11T12:07:27+08:00 ][ log ] 【命令响应】成功更新命令 '56652' 的状态为 'acked'
log
[ 2025-08-11T12:07:51+08:00 ][ error ] 收到消息：dz/pi/getstatus/710001
error
[ 2025-08-11T12:07:51+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710001',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710001","SIMID":"89861118167312012425","CS":"0","LS":"1","SS":"0","BS":"10.9","RSSI":23,"MT":"4","NG":"0"}',
  'timestamp' => '2025-08-11 12:07:51',
)
error
[ 2025-08-11T12:07:51+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T12:07:51+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710001',
  'SIMID' => '89861118167312012425',
  'CS' => '0',
  'LS' => '1',
  'SS' => '0',
  'BS' => '10.9',
  'RSSI' => 23,
  'MT' => '4',
  'NG' => '0',
)
error
[ 2025-08-11T12:07:51+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710001
error
[ 2025-08-11T12:07:51+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-11T12:07:51+08:00 ][ log ] 【状态上报】开始处理地锁 '710001' 的通用状态...
log
[ 2025-08-11T12:07:51+08:00 ][ log ] 【状态上报】成功更新地锁 '710001' 的快照状态到数据库
log
[ 2025-08-11T12:07:51+08:00 ][ log ] 【状态上报】成功记录地锁 '710001' 的状态到日志表
log
[ 2025-08-11T12:07:51+08:00 ][ log ] 【业务触发】检查 '710001' 的状态变化...
log
[ 2025-08-11T12:07:51+08:00 ][ log ] 【业务触发】检测到地锁 '710001' 关锁成功，开始处理订单...
log
[ 2025-08-11T12:07:51+08:00 ][ error ] 收到消息：dz/pi/setack/710001
error
[ 2025-08-11T12:07:51+08:00 ][ error ] array (
  'topic' => 'dz/pi/setack/710001',
  'message' => '{"VER":"0","CMD":"17","CD":"710001","CL":"1","PACKID":50729,"STATUS":"81"}',
  'timestamp' => '2025-08-11 12:07:51',
)
error
[ 2025-08-11T12:07:51+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T12:07:51+08:00 ][ error ] array (
  'VER' => '0',
  'CMD' => '17',
  'CD' => '710001',
  'CL' => '1',
  'PACKID' => 50729,
  'STATUS' => '81',
)
error
[ 2025-08-11T12:07:51+08:00 ][ error ] 收到命令响应消息，待处理: dz/pi/setack/710001
error
[ 2025-08-11T12:07:51+08:00 ][ log ] 【命令响应】收到针对命令包 '50729' 的ACK确认
log
[ 2025-08-11T12:07:51+08:00 ][ log ] 【命令响应】成功更新命令 '50729' 的状态为 'acked'
log
[ 2025-08-11T12:08:05+08:00 ][ error ] 收到消息：dz/pi/getstatus/710001
error
[ 2025-08-11T12:08:05+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710001',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710001","SIMID":"89861118167312012425","CS":"0","LS":"0","SS":"0","BS":"11.3","RSSI":23,"MT":"4","NG":"0"}',
  'timestamp' => '2025-08-11 12:08:05',
)
error
[ 2025-08-11T12:08:05+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T12:08:05+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710001',
  'SIMID' => '89861118167312012425',
  'CS' => '0',
  'LS' => '0',
  'SS' => '0',
  'BS' => '11.3',
  'RSSI' => 23,
  'MT' => '4',
  'NG' => '0',
)
error
[ 2025-08-11T12:08:05+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710001
error
[ 2025-08-11T12:08:05+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-11T12:08:05+08:00 ][ log ] 【状态上报】开始处理地锁 '710001' 的通用状态...
log
[ 2025-08-11T12:08:05+08:00 ][ log ] 【状态上报】成功更新地锁 '710001' 的快照状态到数据库
log
[ 2025-08-11T12:08:05+08:00 ][ log ] 【状态上报】成功记录地锁 '710001' 的状态到日志表
log
[ 2025-08-11T12:08:05+08:00 ][ log ] 【业务触发】检查 '710001' 的状态变化...
log
[ 2025-08-11T12:08:05+08:00 ][ log ] 【业务触发】检测到地锁 '710001' 开锁成功，结束处理订单...
log
[ 2025-08-11T12:08:05+08:00 ][ log ] 打印order
log
[ 2025-08-11T12:08:05+08:00 ][ log ] array (
  'id' => 56,
  'platform_id' => 20,
  'agent_id' => 82,
  'hospital_id' => 48,
  'hospital_fcbl' => 20.0,
  'hospital_price' => '1.00',
  'hospital_hourlong' => 1,
  'hospital_freedt' => 999999,
  'departments_id' => 34,
  'equipment_id' => 2,
  'equipment_info_id' => 0,
  'info' => '总平台-深圳代理商-南山物业-物业北车棚-设备710001-710001',
  'sn' => 'ord2025081112074781820190',
  'user_id' => 5611,
  'money' => '0.00',
  'status' => '1',
  'pay_types' => '0',
  'pay_status' => '0',
  'pay_time' => NULL,
  'createtime' => 1754885271,
  'returntime' => NULL,
  'updatetime' => 1754885267,
  'is_branch' => '1',
  'admin_info' => NULL,
  'timelong' => 0,
  'timelong_fenzhong' => 0,
  'really_money' => '0.00',
  'actreturntime' => NULL,
  'answer_return_time' => 1754924400,
  'overtime' => NULL,
  'overtime_money' => NULL,
  'normal_money' => NULL,
  'charging_rule' => 1,
  'use_status' => 2,
  'nb_goodsid' => '',
)
log
[ 2025-08-11T12:08:05+08:00 ][ error ] 【业务触发】订单(SN:ord2025081112074781820190)结束逻辑处理成功
error
[ 2025-08-11T12:08:05+08:00 ][ error ] 收到消息：dz/pi/setack/710001
error
[ 2025-08-11T12:08:05+08:00 ][ error ] array (
  'topic' => 'dz/pi/setack/710001',
  'message' => '{"VER":"0","CMD":"17","CD":"710001","CL":"0","PACKID":41706,"STATUS":"1"}',
  'timestamp' => '2025-08-11 12:08:05',
)
error
[ 2025-08-11T12:08:05+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T12:08:05+08:00 ][ error ] array (
  'VER' => '0',
  'CMD' => '17',
  'CD' => '710001',
  'CL' => '0',
  'PACKID' => 41706,
  'STATUS' => '1',
)
error
[ 2025-08-11T12:08:05+08:00 ][ error ] 收到命令响应消息，待处理: dz/pi/setack/710001
error
[ 2025-08-11T12:08:05+08:00 ][ log ] 【命令响应】收到针对命令包 '41706' 的ACK确认
log
[ 2025-08-11T12:08:05+08:00 ][ log ] 【命令响应】成功更新命令 '41706' 的状态为 'acked'
log
[ 2025-08-11T12:11:50+08:00 ][ error ] 收到消息：dz/pi/getstatus/710001
error
[ 2025-08-11T12:11:50+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710001',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710001","SIMID":"89861118167312012425","CS":"0","LS":"1","SS":"0","BS":"10.2","RSSI":23,"MT":"4","NG":"0"}',
  'timestamp' => '2025-08-11 12:11:50',
)
error
[ 2025-08-11T12:11:50+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T12:11:50+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710001',
  'SIMID' => '89861118167312012425',
  'CS' => '0',
  'LS' => '1',
  'SS' => '0',
  'BS' => '10.2',
  'RSSI' => 23,
  'MT' => '4',
  'NG' => '0',
)
error
[ 2025-08-11T12:11:50+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710001
error
[ 2025-08-11T12:11:50+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-11T12:11:50+08:00 ][ log ] 【状态上报】开始处理地锁 '710001' 的通用状态...
log
[ 2025-08-11T12:11:50+08:00 ][ log ] 【状态上报】成功更新地锁 '710001' 的快照状态到数据库
log
[ 2025-08-11T12:11:50+08:00 ][ log ] 【状态上报】成功记录地锁 '710001' 的状态到日志表
log
[ 2025-08-11T12:11:50+08:00 ][ log ] 【业务触发】检查 '710001' 的状态变化...
log
[ 2025-08-11T12:11:50+08:00 ][ log ] 【业务触发】检测到地锁 '710001' 关锁成功，开始处理订单...
log
[ 2025-08-11T12:11:50+08:00 ][ error ] 收到消息：dz/pi/setack/710001
error
[ 2025-08-11T12:11:50+08:00 ][ error ] array (
  'topic' => 'dz/pi/setack/710001',
  'message' => '{"VER":"0","CMD":"17","CD":"710001","CL":"1","PACKID":22379,"STATUS":"81"}',
  'timestamp' => '2025-08-11 12:11:50',
)
error
[ 2025-08-11T12:11:50+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T12:11:50+08:00 ][ error ] array (
  'VER' => '0',
  'CMD' => '17',
  'CD' => '710001',
  'CL' => '1',
  'PACKID' => 22379,
  'STATUS' => '81',
)
error
[ 2025-08-11T12:11:50+08:00 ][ error ] 收到命令响应消息，待处理: dz/pi/setack/710001
error
[ 2025-08-11T12:11:50+08:00 ][ log ] 【命令响应】收到针对命令包 '22379' 的ACK确认
log
[ 2025-08-11T12:11:50+08:00 ][ log ] 【命令响应】成功更新命令 '22379' 的状态为 'acked'
log
[ 2025-08-11T12:12:03+08:00 ][ error ] 收到消息：dz/pi/getstatus/710001
error
[ 2025-08-11T12:12:03+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710001',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710001","SIMID":"89861118167312012425","CS":"0","LS":"0","SS":"0","BS":"11.4","RSSI":23,"MT":"4","NG":"0"}',
  'timestamp' => '2025-08-11 12:12:03',
)
error
[ 2025-08-11T12:12:03+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T12:12:03+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710001',
  'SIMID' => '89861118167312012425',
  'CS' => '0',
  'LS' => '0',
  'SS' => '0',
  'BS' => '11.4',
  'RSSI' => 23,
  'MT' => '4',
  'NG' => '0',
)
error
[ 2025-08-11T12:12:03+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710001
error
[ 2025-08-11T12:12:03+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-11T12:12:03+08:00 ][ log ] 【状态上报】开始处理地锁 '710001' 的通用状态...
log
[ 2025-08-11T12:12:03+08:00 ][ log ] 【状态上报】成功更新地锁 '710001' 的快照状态到数据库
log
[ 2025-08-11T12:12:03+08:00 ][ log ] 【状态上报】成功记录地锁 '710001' 的状态到日志表
log
[ 2025-08-11T12:12:03+08:00 ][ log ] 【业务触发】检查 '710001' 的状态变化...
log
[ 2025-08-11T12:12:03+08:00 ][ log ] 【业务触发】检测到地锁 '710001' 开锁成功，结束处理订单...
log
[ 2025-08-11T12:12:03+08:00 ][ log ] 打印order
log
[ 2025-08-11T12:12:03+08:00 ][ log ] array (
  'id' => 57,
  'platform_id' => 20,
  'agent_id' => 82,
  'hospital_id' => 48,
  'hospital_fcbl' => 20.0,
  'hospital_price' => '1.00',
  'hospital_hourlong' => 1,
  'hospital_freedt' => 999999,
  'departments_id' => 34,
  'equipment_id' => 2,
  'equipment_info_id' => 0,
  'info' => '总平台-深圳代理商-南山物业-物业北车棚-设备710001-710001',
  'sn' => 'ord2025081112114698342559',
  'user_id' => 5611,
  'money' => '0.00',
  'status' => '1',
  'pay_types' => '0',
  'pay_status' => '0',
  'pay_time' => NULL,
  'createtime' => 1754885510,
  'returntime' => NULL,
  'updatetime' => 1754885506,
  'is_branch' => '1',
  'admin_info' => NULL,
  'timelong' => 0,
  'timelong_fenzhong' => 0,
  'really_money' => '0.00',
  'actreturntime' => NULL,
  'answer_return_time' => 1754924400,
  'overtime' => NULL,
  'overtime_money' => NULL,
  'normal_money' => NULL,
  'charging_rule' => 1,
  'use_status' => 2,
  'nb_goodsid' => '',
)
log
[ 2025-08-11T12:12:03+08:00 ][ error ] 【业务触发】订单(SN:ord2025081112114698342559)结束逻辑处理成功
error
[ 2025-08-11T12:12:03+08:00 ][ error ] 收到消息：dz/pi/setack/710001
error
[ 2025-08-11T12:12:03+08:00 ][ error ] array (
  'topic' => 'dz/pi/setack/710001',
  'message' => '{"VER":"0","CMD":"17","CD":"710001","CL":"0","PACKID":54041,"STATUS":"1"}',
  'timestamp' => '2025-08-11 12:12:03',
)
error
[ 2025-08-11T12:12:03+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T12:12:03+08:00 ][ error ] array (
  'VER' => '0',
  'CMD' => '17',
  'CD' => '710001',
  'CL' => '0',
  'PACKID' => 54041,
  'STATUS' => '1',
)
error
[ 2025-08-11T12:12:03+08:00 ][ error ] 收到命令响应消息，待处理: dz/pi/setack/710001
error
[ 2025-08-11T12:12:03+08:00 ][ log ] 【命令响应】收到针对命令包 '54041' 的ACK确认
log
[ 2025-08-11T12:12:03+08:00 ][ log ] 【命令响应】成功更新命令 '54041' 的状态为 'acked'
log
[ 2025-08-11T12:22:03+08:00 ][ error ] 收到消息：dz/pi/getstatus/710001
error
[ 2025-08-11T12:22:03+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710001',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710001","SIMID":"89861118167312012425","CS":"0","LS":"0","SS":"0","BS":"12.2","RSSI":23,"MT":"20","NG":"0"}',
  'timestamp' => '2025-08-11 12:22:03',
)
error
[ 2025-08-11T12:22:03+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T12:22:03+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710001',
  'SIMID' => '89861118167312012425',
  'CS' => '0',
  'LS' => '0',
  'SS' => '0',
  'BS' => '12.2',
  'RSSI' => 23,
  'MT' => '20',
  'NG' => '0',
)
error
[ 2025-08-11T12:22:03+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710001
error
[ 2025-08-11T12:22:03+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-11T12:22:03+08:00 ][ log ] 【状态上报】开始处理地锁 '710001' 的通用状态...
log
[ 2025-08-11T12:22:03+08:00 ][ log ] 【状态上报】成功更新地锁 '710001' 的快照状态到数据库
log
[ 2025-08-11T12:22:03+08:00 ][ log ] 【状态上报】成功记录地锁 '710001' 的状态到日志表
log
[ 2025-08-11T12:22:03+08:00 ][ log ] 【业务触发】检查 '710001' 的状态变化...
log
[ 2025-08-11T12:32:02+08:00 ][ error ] 收到消息：dz/pi/getstatus/710001
error
[ 2025-08-11T12:32:02+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710001',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710001","SIMID":"89861118167312012425","CS":"0","LS":"0","SS":"0","BS":"12.2","RSSI":23,"MT":"20","NG":"0"}',
  'timestamp' => '2025-08-11 12:32:02',
)
error
[ 2025-08-11T12:32:02+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T12:32:02+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710001',
  'SIMID' => '89861118167312012425',
  'CS' => '0',
  'LS' => '0',
  'SS' => '0',
  'BS' => '12.2',
  'RSSI' => 23,
  'MT' => '20',
  'NG' => '0',
)
error
[ 2025-08-11T12:32:02+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710001
error
[ 2025-08-11T12:32:02+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-11T12:32:02+08:00 ][ log ] 【状态上报】开始处理地锁 '710001' 的通用状态...
log
[ 2025-08-11T12:32:02+08:00 ][ log ] 【状态上报】成功更新地锁 '710001' 的快照状态到数据库
log
[ 2025-08-11T12:32:02+08:00 ][ log ] 【状态上报】成功记录地锁 '710001' 的状态到日志表
log
[ 2025-08-11T12:32:02+08:00 ][ log ] 【业务触发】检查 '710001' 的状态变化...
log
[ 2025-08-11T12:35:04+08:00 ][ error ] 收到消息：dz/pi/getstatus/710001
error
[ 2025-08-11T12:35:04+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710001',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710001","SIMID":"89861118167312012425","CS":"0","LS":"1","SS":"0","BS":"10.9","RSSI":23,"MT":"4","NG":"0"}',
  'timestamp' => '2025-08-11 12:35:04',
)
error
[ 2025-08-11T12:35:04+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T12:35:04+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710001',
  'SIMID' => '89861118167312012425',
  'CS' => '0',
  'LS' => '1',
  'SS' => '0',
  'BS' => '10.9',
  'RSSI' => 23,
  'MT' => '4',
  'NG' => '0',
)
error
[ 2025-08-11T12:35:04+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710001
error
[ 2025-08-11T12:35:04+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-11T12:35:04+08:00 ][ log ] 【状态上报】开始处理地锁 '710001' 的通用状态...
log
[ 2025-08-11T12:35:04+08:00 ][ log ] 【状态上报】成功更新地锁 '710001' 的快照状态到数据库
log
[ 2025-08-11T12:35:04+08:00 ][ log ] 【状态上报】成功记录地锁 '710001' 的状态到日志表
log
[ 2025-08-11T12:35:04+08:00 ][ log ] 【业务触发】检查 '710001' 的状态变化...
log
[ 2025-08-11T12:35:04+08:00 ][ log ] 【业务触发】检测到地锁 '710001' 关锁成功，开始处理订单...
log
[ 2025-08-11T12:35:04+08:00 ][ error ] 收到消息：dz/pi/setack/710001
error
[ 2025-08-11T12:35:04+08:00 ][ error ] array (
  'topic' => 'dz/pi/setack/710001',
  'message' => '{"VER":"0","CMD":"17","CD":"710001","CL":"1","PACKID":23103,"STATUS":"81"}',
  'timestamp' => '2025-08-11 12:35:04',
)
error
[ 2025-08-11T12:35:04+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T12:35:04+08:00 ][ error ] array (
  'VER' => '0',
  'CMD' => '17',
  'CD' => '710001',
  'CL' => '1',
  'PACKID' => 23103,
  'STATUS' => '81',
)
error
[ 2025-08-11T12:35:04+08:00 ][ error ] 收到命令响应消息，待处理: dz/pi/setack/710001
error
[ 2025-08-11T12:35:04+08:00 ][ log ] 【命令响应】收到针对命令包 '23103' 的ACK确认
log
[ 2025-08-11T12:35:04+08:00 ][ log ] 【命令响应】成功更新命令 '23103' 的状态为 'acked'
log
[ 2025-08-11T12:35:15+08:00 ][ error ] 收到消息：dz/pi/getstatus/710001
error
[ 2025-08-11T12:35:15+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710001',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710001","SIMID":"89861118167312012425","CS":"0","LS":"0","SS":"0","BS":"11.4","RSSI":23,"MT":"4","NG":"0"}',
  'timestamp' => '2025-08-11 12:35:15',
)
error
[ 2025-08-11T12:35:15+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T12:35:15+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710001',
  'SIMID' => '89861118167312012425',
  'CS' => '0',
  'LS' => '0',
  'SS' => '0',
  'BS' => '11.4',
  'RSSI' => 23,
  'MT' => '4',
  'NG' => '0',
)
error
[ 2025-08-11T12:35:15+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710001
error
[ 2025-08-11T12:35:15+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-11T12:35:15+08:00 ][ log ] 【状态上报】开始处理地锁 '710001' 的通用状态...
log
[ 2025-08-11T12:35:15+08:00 ][ log ] 【状态上报】成功更新地锁 '710001' 的快照状态到数据库
log
[ 2025-08-11T12:35:15+08:00 ][ log ] 【状态上报】成功记录地锁 '710001' 的状态到日志表
log
[ 2025-08-11T12:35:15+08:00 ][ log ] 【业务触发】检查 '710001' 的状态变化...
log
[ 2025-08-11T12:35:15+08:00 ][ log ] 【业务触发】检测到地锁 '710001' 开锁成功，结束处理订单...
log
[ 2025-08-11T12:35:15+08:00 ][ log ] 打印order
log
[ 2025-08-11T12:35:15+08:00 ][ log ] array (
  'id' => 58,
  'platform_id' => 20,
  'agent_id' => 82,
  'hospital_id' => 48,
  'hospital_fcbl' => 20.0,
  'hospital_price' => '1.00',
  'hospital_hourlong' => 1,
  'hospital_freedt' => 999999,
  'departments_id' => 34,
  'equipment_id' => 2,
  'equipment_info_id' => 0,
  'info' => '总平台-深圳代理商-南山物业-物业北车棚-设备710001-710001',
  'sn' => 'ord2025081112350072204484',
  'user_id' => 5611,
  'money' => '0.00',
  'status' => '1',
  'pay_types' => '0',
  'pay_status' => '0',
  'pay_time' => NULL,
  'createtime' => 1754886904,
  'returntime' => NULL,
  'updatetime' => 1754886900,
  'is_branch' => '1',
  'admin_info' => NULL,
  'timelong' => 0,
  'timelong_fenzhong' => 0,
  'really_money' => '0.00',
  'actreturntime' => NULL,
  'answer_return_time' => 1754924400,
  'overtime' => NULL,
  'overtime_money' => NULL,
  'normal_money' => NULL,
  'charging_rule' => 1,
  'use_status' => 2,
  'nb_goodsid' => '',
)
log
[ 2025-08-11T12:35:15+08:00 ][ error ] 【业务触发】订单(SN:ord2025081112350072204484)结束逻辑处理成功
error
[ 2025-08-11T12:35:15+08:00 ][ error ] 收到消息：dz/pi/setack/710001
error
[ 2025-08-11T12:35:15+08:00 ][ error ] array (
  'topic' => 'dz/pi/setack/710001',
  'message' => '{"VER":"0","CMD":"17","CD":"710001","CL":"0","PACKID":14649,"STATUS":"1"}',
  'timestamp' => '2025-08-11 12:35:15',
)
error
[ 2025-08-11T12:35:15+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T12:35:15+08:00 ][ error ] array (
  'VER' => '0',
  'CMD' => '17',
  'CD' => '710001',
  'CL' => '0',
  'PACKID' => 14649,
  'STATUS' => '1',
)
error
[ 2025-08-11T12:35:15+08:00 ][ error ] 收到命令响应消息，待处理: dz/pi/setack/710001
error
[ 2025-08-11T12:35:15+08:00 ][ log ] 【命令响应】收到针对命令包 '14649' 的ACK确认
log
[ 2025-08-11T12:35:15+08:00 ][ log ] 【命令响应】成功更新命令 '14649' 的状态为 'acked'
log
[ 2025-08-11T12:45:15+08:00 ][ error ] 收到消息：dz/pi/getstatus/710001
error
[ 2025-08-11T12:45:15+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710001',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710001","SIMID":"89861118167312012425","CS":"0","LS":"0","SS":"0","BS":"12.3","RSSI":23,"MT":"20","NG":"0"}',
  'timestamp' => '2025-08-11 12:45:15',
)
error
[ 2025-08-11T12:45:15+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T12:45:15+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710001',
  'SIMID' => '89861118167312012425',
  'CS' => '0',
  'LS' => '0',
  'SS' => '0',
  'BS' => '12.3',
  'RSSI' => 23,
  'MT' => '20',
  'NG' => '0',
)
error
[ 2025-08-11T12:45:15+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710001
error
[ 2025-08-11T12:45:15+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-11T12:45:15+08:00 ][ log ] 【状态上报】开始处理地锁 '710001' 的通用状态...
log
[ 2025-08-11T12:45:15+08:00 ][ log ] 【状态上报】成功更新地锁 '710001' 的快照状态到数据库
log
[ 2025-08-11T12:45:15+08:00 ][ log ] 【状态上报】成功记录地锁 '710001' 的状态到日志表
log
[ 2025-08-11T12:45:15+08:00 ][ log ] 【业务触发】检查 '710001' 的状态变化...
log
[ 2025-08-11T12:55:14+08:00 ][ error ] 收到消息：dz/pi/getstatus/710001
error
[ 2025-08-11T12:55:14+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710001',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710001","SIMID":"89861118167312012425","CS":"0","LS":"0","SS":"0","BS":"12.3","RSSI":23,"MT":"20","NG":"0"}',
  'timestamp' => '2025-08-11 12:55:14',
)
error
[ 2025-08-11T12:55:14+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T12:55:14+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710001',
  'SIMID' => '89861118167312012425',
  'CS' => '0',
  'LS' => '0',
  'SS' => '0',
  'BS' => '12.3',
  'RSSI' => 23,
  'MT' => '20',
  'NG' => '0',
)
error
[ 2025-08-11T12:55:14+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710001
error
[ 2025-08-11T12:55:14+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-11T12:55:14+08:00 ][ log ] 【状态上报】开始处理地锁 '710001' 的通用状态...
log
[ 2025-08-11T12:55:14+08:00 ][ log ] 【状态上报】成功更新地锁 '710001' 的快照状态到数据库
log
[ 2025-08-11T12:55:14+08:00 ][ log ] 【状态上报】成功记录地锁 '710001' 的状态到日志表
log
[ 2025-08-11T12:55:14+08:00 ][ log ] 【业务触发】检查 '710001' 的状态变化...
log
[ 2025-08-11T13:05:13+08:00 ][ error ] 收到消息：dz/pi/getstatus/710001
error
[ 2025-08-11T13:05:13+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710001',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710001","SIMID":"89861118167312012425","CS":"0","LS":"0","SS":"0","BS":"12.3","RSSI":23,"MT":"20","NG":"0"}',
  'timestamp' => '2025-08-11 13:05:13',
)
error
[ 2025-08-11T13:05:13+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T13:05:13+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710001',
  'SIMID' => '89861118167312012425',
  'CS' => '0',
  'LS' => '0',
  'SS' => '0',
  'BS' => '12.3',
  'RSSI' => 23,
  'MT' => '20',
  'NG' => '0',
)
error
[ 2025-08-11T13:05:13+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710001
error
[ 2025-08-11T13:05:13+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-11T13:05:13+08:00 ][ log ] 【状态上报】开始处理地锁 '710001' 的通用状态...
log
[ 2025-08-11T13:05:13+08:00 ][ log ] 【状态上报】成功更新地锁 '710001' 的快照状态到数据库
log
[ 2025-08-11T13:05:13+08:00 ][ log ] 【状态上报】成功记录地锁 '710001' 的状态到日志表
log
[ 2025-08-11T13:05:13+08:00 ][ log ] 【业务触发】检查 '710001' 的状态变化...
log
[ 2025-08-11T13:15:12+08:00 ][ error ] 收到消息：dz/pi/getstatus/710001
error
[ 2025-08-11T13:15:12+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710001',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710001","SIMID":"89861118167312012425","CS":"0","LS":"0","SS":"0","BS":"12.3","RSSI":23,"MT":"20","NG":"0"}',
  'timestamp' => '2025-08-11 13:15:12',
)
error
[ 2025-08-11T13:15:12+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T13:15:12+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710001',
  'SIMID' => '89861118167312012425',
  'CS' => '0',
  'LS' => '0',
  'SS' => '0',
  'BS' => '12.3',
  'RSSI' => 23,
  'MT' => '20',
  'NG' => '0',
)
error
[ 2025-08-11T13:15:12+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710001
error
[ 2025-08-11T13:15:12+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-11T13:15:12+08:00 ][ log ] 【状态上报】开始处理地锁 '710001' 的通用状态...
log
[ 2025-08-11T13:15:12+08:00 ][ log ] 【状态上报】成功更新地锁 '710001' 的快照状态到数据库
log
[ 2025-08-11T13:15:12+08:00 ][ log ] 【状态上报】成功记录地锁 '710001' 的状态到日志表
log
[ 2025-08-11T13:15:12+08:00 ][ log ] 【业务触发】检查 '710001' 的状态变化...
log
[ 2025-08-11T13:25:11+08:00 ][ error ] 收到消息：dz/pi/getstatus/710001
error
[ 2025-08-11T13:25:11+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710001',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710001","SIMID":"89861118167312012425","CS":"0","LS":"0","SS":"0","BS":"12.3","RSSI":23,"MT":"20","NG":"0"}',
  'timestamp' => '2025-08-11 13:25:11',
)
error
[ 2025-08-11T13:25:11+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T13:25:11+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710001',
  'SIMID' => '89861118167312012425',
  'CS' => '0',
  'LS' => '0',
  'SS' => '0',
  'BS' => '12.3',
  'RSSI' => 23,
  'MT' => '20',
  'NG' => '0',
)
error
[ 2025-08-11T13:25:11+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710001
error
[ 2025-08-11T13:25:11+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-11T13:25:11+08:00 ][ log ] 【状态上报】开始处理地锁 '710001' 的通用状态...
log
[ 2025-08-11T13:25:11+08:00 ][ log ] 【状态上报】成功更新地锁 '710001' 的快照状态到数据库
log
[ 2025-08-11T13:25:11+08:00 ][ log ] 【状态上报】成功记录地锁 '710001' 的状态到日志表
log
[ 2025-08-11T13:25:11+08:00 ][ log ] 【业务触发】检查 '710001' 的状态变化...
log
[ 2025-08-11T13:35:09+08:00 ][ error ] 收到消息：dz/pi/getstatus/710001
error
[ 2025-08-11T13:35:09+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710001',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710001","SIMID":"89861118167312012425","CS":"0","LS":"0","SS":"0","BS":"12.3","RSSI":23,"MT":"20","NG":"0"}',
  'timestamp' => '2025-08-11 13:35:09',
)
error
[ 2025-08-11T13:35:09+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T13:35:09+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710001',
  'SIMID' => '89861118167312012425',
  'CS' => '0',
  'LS' => '0',
  'SS' => '0',
  'BS' => '12.3',
  'RSSI' => 23,
  'MT' => '20',
  'NG' => '0',
)
error
[ 2025-08-11T13:35:09+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710001
error
[ 2025-08-11T13:35:09+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-11T13:35:09+08:00 ][ log ] 【状态上报】开始处理地锁 '710001' 的通用状态...
log
[ 2025-08-11T13:35:09+08:00 ][ log ] 【状态上报】成功更新地锁 '710001' 的快照状态到数据库
log
[ 2025-08-11T13:35:09+08:00 ][ log ] 【状态上报】成功记录地锁 '710001' 的状态到日志表
log
[ 2025-08-11T13:35:09+08:00 ][ log ] 【业务触发】检查 '710001' 的状态变化...
log
[ 2025-08-11T13:38:49+08:00 ][ error ] 收到消息：dz/pi/getstatus/710001
error
[ 2025-08-11T13:38:49+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710001',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710001","SIMID":"89861118167312012425","CS":"0","LS":"1","SS":"0","BS":"10.9","RSSI":23,"MT":"4","NG":"0"}',
  'timestamp' => '2025-08-11 13:38:49',
)
error
[ 2025-08-11T13:38:49+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T13:38:49+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710001',
  'SIMID' => '89861118167312012425',
  'CS' => '0',
  'LS' => '1',
  'SS' => '0',
  'BS' => '10.9',
  'RSSI' => 23,
  'MT' => '4',
  'NG' => '0',
)
error
[ 2025-08-11T13:38:49+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710001
error
[ 2025-08-11T13:38:49+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-11T13:38:49+08:00 ][ log ] 【状态上报】开始处理地锁 '710001' 的通用状态...
log
[ 2025-08-11T13:38:49+08:00 ][ log ] 【状态上报】成功更新地锁 '710001' 的快照状态到数据库
log
[ 2025-08-11T13:38:49+08:00 ][ log ] 【状态上报】成功记录地锁 '710001' 的状态到日志表
log
[ 2025-08-11T13:38:49+08:00 ][ log ] 【业务触发】检查 '710001' 的状态变化...
log
[ 2025-08-11T13:38:49+08:00 ][ log ] 【业务触发】检测到地锁 '710001' 关锁成功，开始处理订单...
log
[ 2025-08-11T13:38:49+08:00 ][ error ] 收到消息：dz/pi/setack/710001
error
[ 2025-08-11T13:38:49+08:00 ][ error ] array (
  'topic' => 'dz/pi/setack/710001',
  'message' => '{"VER":"0","CMD":"17","CD":"710001","CL":"1","PACKID":2079,"STATUS":"81"}',
  'timestamp' => '2025-08-11 13:38:49',
)
error
[ 2025-08-11T13:38:49+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T13:38:49+08:00 ][ error ] array (
  'VER' => '0',
  'CMD' => '17',
  'CD' => '710001',
  'CL' => '1',
  'PACKID' => 2079,
  'STATUS' => '81',
)
error
[ 2025-08-11T13:38:49+08:00 ][ error ] 收到命令响应消息，待处理: dz/pi/setack/710001
error
[ 2025-08-11T13:38:49+08:00 ][ log ] 【命令响应】收到针对命令包 '2079' 的ACK确认
log
[ 2025-08-11T13:38:49+08:00 ][ log ] 【命令响应】成功更新命令 '2079' 的状态为 'acked'
log
[ 2025-08-11T13:39:18+08:00 ][ error ] 收到消息：dz/pi/getstatus/710001
error
[ 2025-08-11T13:39:18+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710001',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710001","SIMID":"89861118167312012425","CS":"0","LS":"0","SS":"0","BS":"11.4","RSSI":23,"MT":"4","NG":"0"}',
  'timestamp' => '2025-08-11 13:39:18',
)
error
[ 2025-08-11T13:39:18+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T13:39:18+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710001',
  'SIMID' => '89861118167312012425',
  'CS' => '0',
  'LS' => '0',
  'SS' => '0',
  'BS' => '11.4',
  'RSSI' => 23,
  'MT' => '4',
  'NG' => '0',
)
error
[ 2025-08-11T13:39:18+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710001
error
[ 2025-08-11T13:39:18+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-11T13:39:18+08:00 ][ log ] 【状态上报】开始处理地锁 '710001' 的通用状态...
log
[ 2025-08-11T13:39:18+08:00 ][ log ] 【状态上报】成功更新地锁 '710001' 的快照状态到数据库
log
[ 2025-08-11T13:39:18+08:00 ][ log ] 【状态上报】成功记录地锁 '710001' 的状态到日志表
log
[ 2025-08-11T13:39:18+08:00 ][ log ] 【业务触发】检查 '710001' 的状态变化...
log
[ 2025-08-11T13:39:18+08:00 ][ log ] 【业务触发】检测到地锁 '710001' 开锁成功，结束处理订单...
log
[ 2025-08-11T13:39:18+08:00 ][ log ] 打印order
log
[ 2025-08-11T13:39:18+08:00 ][ log ] array (
  'id' => 59,
  'platform_id' => 20,
  'agent_id' => 82,
  'hospital_id' => 48,
  'hospital_fcbl' => 20.0,
  'hospital_price' => '1.00',
  'hospital_hourlong' => 1,
  'hospital_freedt' => 999999,
  'departments_id' => 34,
  'equipment_id' => 2,
  'equipment_info_id' => 0,
  'info' => '总平台-深圳代理商-南山物业-物业北车棚-设备710001-710001',
  'sn' => 'ord2025081113384497366091',
  'user_id' => 5613,
  'money' => '0.00',
  'status' => '1',
  'pay_types' => '0',
  'pay_status' => '0',
  'pay_time' => NULL,
  'createtime' => 1754890729,
  'returntime' => NULL,
  'updatetime' => 1754890724,
  'is_branch' => '1',
  'admin_info' => NULL,
  'timelong' => 0,
  'timelong_fenzhong' => 0,
  'really_money' => '0.00',
  'actreturntime' => NULL,
  'answer_return_time' => 1754924400,
  'overtime' => NULL,
  'overtime_money' => NULL,
  'normal_money' => NULL,
  'charging_rule' => 1,
  'use_status' => 2,
  'nb_goodsid' => '',
)
log
[ 2025-08-11T13:39:18+08:00 ][ error ] 【业务触发】订单(SN:ord2025081113384497366091)结束逻辑处理成功
error
[ 2025-08-11T13:39:18+08:00 ][ error ] 收到消息：dz/pi/setack/710001
error
[ 2025-08-11T13:39:18+08:00 ][ error ] array (
  'topic' => 'dz/pi/setack/710001',
  'message' => '{"VER":"0","CMD":"17","CD":"710001","CL":"0","PACKID":22924,"STATUS":"1"}',
  'timestamp' => '2025-08-11 13:39:18',
)
error
[ 2025-08-11T13:39:18+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T13:39:18+08:00 ][ error ] array (
  'VER' => '0',
  'CMD' => '17',
  'CD' => '710001',
  'CL' => '0',
  'PACKID' => 22924,
  'STATUS' => '1',
)
error
[ 2025-08-11T13:39:18+08:00 ][ error ] 收到命令响应消息，待处理: dz/pi/setack/710001
error
[ 2025-08-11T13:39:18+08:00 ][ log ] 【命令响应】收到针对命令包 '22924' 的ACK确认
log
[ 2025-08-11T13:39:18+08:00 ][ log ] 【命令响应】成功更新命令 '22924' 的状态为 'acked'
log
[ 2025-08-11T13:49:17+08:00 ][ error ] 收到消息：dz/pi/getstatus/710001
error
[ 2025-08-11T13:49:17+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710001',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710001","SIMID":"89861118167312012425","CS":"0","LS":"0","SS":"0","BS":"12.3","RSSI":23,"MT":"20","NG":"0"}',
  'timestamp' => '2025-08-11 13:49:17',
)
error
[ 2025-08-11T13:49:17+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T13:49:17+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710001',
  'SIMID' => '89861118167312012425',
  'CS' => '0',
  'LS' => '0',
  'SS' => '0',
  'BS' => '12.3',
  'RSSI' => 23,
  'MT' => '20',
  'NG' => '0',
)
error
[ 2025-08-11T13:49:17+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710001
error
[ 2025-08-11T13:49:17+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-11T13:49:17+08:00 ][ log ] 【状态上报】开始处理地锁 '710001' 的通用状态...
log
[ 2025-08-11T13:49:17+08:00 ][ log ] 【状态上报】成功更新地锁 '710001' 的快照状态到数据库
log
[ 2025-08-11T13:49:17+08:00 ][ log ] 【状态上报】成功记录地锁 '710001' 的状态到日志表
log
[ 2025-08-11T13:49:17+08:00 ][ log ] 【业务触发】检查 '710001' 的状态变化...
log
[ 2025-08-11T13:59:16+08:00 ][ error ] 收到消息：dz/pi/getstatus/710001
error
[ 2025-08-11T13:59:16+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710001',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710001","SIMID":"89861118167312012425","CS":"0","LS":"0","SS":"0","BS":"12.3","RSSI":23,"MT":"20","NG":"0"}',
  'timestamp' => '2025-08-11 13:59:16',
)
error
[ 2025-08-11T13:59:16+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T13:59:16+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710001',
  'SIMID' => '89861118167312012425',
  'CS' => '0',
  'LS' => '0',
  'SS' => '0',
  'BS' => '12.3',
  'RSSI' => 23,
  'MT' => '20',
  'NG' => '0',
)
error
[ 2025-08-11T13:59:16+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710001
error
[ 2025-08-11T13:59:16+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-11T13:59:16+08:00 ][ log ] 【状态上报】开始处理地锁 '710001' 的通用状态...
log
[ 2025-08-11T13:59:16+08:00 ][ log ] 【状态上报】成功更新地锁 '710001' 的快照状态到数据库
log
[ 2025-08-11T13:59:16+08:00 ][ log ] 【状态上报】成功记录地锁 '710001' 的状态到日志表
log
[ 2025-08-11T13:59:16+08:00 ][ log ] 【业务触发】检查 '710001' 的状态变化...
log
[ 2025-08-11T14:16:03+08:00 ][ error ] 收到消息：dz/pi/mstatus/710001
error
[ 2025-08-11T14:16:03+08:00 ][ error ] array (
  'topic' => 'dz/pi/mstatus/710001',
  'message' => '{"MD":"710001","MS":"0","SS":"0"}',
  'timestamp' => '2025-08-11 14:16:03',
)
error
[ 2025-08-11T14:16:03+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T14:16:03+08:00 ][ error ] array (
  'MD' => '710001',
  'MS' => '0',
  'SS' => '0',
)
error
[ 2025-08-11T14:16:03+08:00 ][ log ] 收到地锁 '710001' 的状态更新: 离线, 信号强度: 0
log
[ 2025-08-11T14:16:03+08:00 ][ log ] 成功更新地锁 '710001' 的数据库状态为: 离线
log
[ 2025-08-11T14:28:59+08:00 ][ error ] 收到消息：dz/pi/mstatus/710001
error
[ 2025-08-11T14:28:59+08:00 ][ error ] array (
  'topic' => 'dz/pi/mstatus/710001',
  'message' => '{"MD":"710001","MS":"1","SS":"21"}',
  'timestamp' => '2025-08-11 14:28:59',
)
error
[ 2025-08-11T14:28:59+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T14:28:59+08:00 ][ error ] array (
  'MD' => '710001',
  'MS' => '1',
  'SS' => '21',
)
error
[ 2025-08-11T14:28:59+08:00 ][ log ] 收到地锁 '710001' 的状态更新: 上线, 信号强度: 21
log
[ 2025-08-11T14:28:59+08:00 ][ log ] 成功更新地锁 '710001' 的数据库状态为: 在线
log
[ 2025-08-11T14:29:00+08:00 ][ error ] 收到消息：dz/pi/getstatus/710001
error
[ 2025-08-11T14:29:00+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710001',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710001","SIMID":"89861118167312012425","CS":"0","LS":"0","SS":"0","BS":"12.2","RSSI":21,"MT":"1","NG":"0"}',
  'timestamp' => '2025-08-11 14:29:00',
)
error
[ 2025-08-11T14:29:00+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T14:29:00+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710001',
  'SIMID' => '89861118167312012425',
  'CS' => '0',
  'LS' => '0',
  'SS' => '0',
  'BS' => '12.2',
  'RSSI' => 21,
  'MT' => '1',
  'NG' => '0',
)
error
[ 2025-08-11T14:29:00+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710001
error
[ 2025-08-11T14:29:00+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-11T14:29:00+08:00 ][ log ] 【状态上报】开始处理地锁 '710001' 的通用状态...
log
[ 2025-08-11T14:29:00+08:00 ][ log ] 【状态上报】成功更新地锁 '710001' 的快照状态到数据库
log
[ 2025-08-11T14:29:00+08:00 ][ log ] 【状态上报】成功记录地锁 '710001' 的状态到日志表
log
[ 2025-08-11T14:29:00+08:00 ][ log ] 【业务触发】检查 '710001' 的状态变化...
log
[ 2025-08-11T14:33:01+08:00 ][ error ] 收到消息：dz/pi/getstatus/710001
error
[ 2025-08-11T14:33:01+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710001',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710001","SIMID":"89861118167312012425","CS":"0","LS":"1","SS":"0","BS":"10.9","RSSI":21,"MT":"4","NG":"0"}',
  'timestamp' => '2025-08-11 14:33:01',
)
error
[ 2025-08-11T14:33:01+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T14:33:01+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710001',
  'SIMID' => '89861118167312012425',
  'CS' => '0',
  'LS' => '1',
  'SS' => '0',
  'BS' => '10.9',
  'RSSI' => 21,
  'MT' => '4',
  'NG' => '0',
)
error
[ 2025-08-11T14:33:01+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710001
error
[ 2025-08-11T14:33:01+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-11T14:33:01+08:00 ][ log ] 【状态上报】开始处理地锁 '710001' 的通用状态...
log
[ 2025-08-11T14:33:01+08:00 ][ log ] 【状态上报】成功更新地锁 '710001' 的快照状态到数据库
log
[ 2025-08-11T14:33:01+08:00 ][ log ] 【状态上报】成功记录地锁 '710001' 的状态到日志表
log
[ 2025-08-11T14:33:01+08:00 ][ log ] 【业务触发】检查 '710001' 的状态变化...
log
[ 2025-08-11T14:33:01+08:00 ][ log ] 【业务触发】检测到地锁 '710001' 关锁成功，开始处理订单...
log
[ 2025-08-11T14:33:01+08:00 ][ error ] 收到消息：dz/pi/setack/710001
error
[ 2025-08-11T14:33:01+08:00 ][ error ] array (
  'topic' => 'dz/pi/setack/710001',
  'message' => '{"VER":"0","CMD":"17","CD":"710001","CL":"1","PACKID":99,"STATUS":"81"}',
  'timestamp' => '2025-08-11 14:33:01',
)
error
[ 2025-08-11T14:33:01+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T14:33:01+08:00 ][ error ] array (
  'VER' => '0',
  'CMD' => '17',
  'CD' => '710001',
  'CL' => '1',
  'PACKID' => 99,
  'STATUS' => '81',
)
error
[ 2025-08-11T14:33:01+08:00 ][ error ] 收到命令响应消息，待处理: dz/pi/setack/710001
error
[ 2025-08-11T14:33:01+08:00 ][ log ] 【命令响应】收到针对命令包 '99' 的ACK确认
log
[ 2025-08-11T14:33:01+08:00 ][ log ] 【命令响应】在日志表中未找到PACKID为 '99' 的命令记录
log
[ 2025-08-11T14:43:00+08:00 ][ error ] 收到消息：dz/pi/getstatus/710001
error
[ 2025-08-11T14:43:00+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710001',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710001","SIMID":"89861118167312012425","CS":"0","LS":"1","SS":"0","BS":"12.3","RSSI":21,"MT":"20","NG":"0"}',
  'timestamp' => '2025-08-11 14:43:00',
)
error
[ 2025-08-11T14:43:00+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T14:43:00+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710001',
  'SIMID' => '89861118167312012425',
  'CS' => '0',
  'LS' => '1',
  'SS' => '0',
  'BS' => '12.3',
  'RSSI' => 21,
  'MT' => '20',
  'NG' => '0',
)
error
[ 2025-08-11T14:43:00+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710001
error
[ 2025-08-11T14:43:00+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-11T14:43:00+08:00 ][ log ] 【状态上报】开始处理地锁 '710001' 的通用状态...
log
[ 2025-08-11T14:43:00+08:00 ][ log ] 【状态上报】成功更新地锁 '710001' 的快照状态到数据库
log
[ 2025-08-11T14:43:00+08:00 ][ log ] 【状态上报】成功记录地锁 '710001' 的状态到日志表
log
[ 2025-08-11T14:43:00+08:00 ][ log ] 【业务触发】检查 '710001' 的状态变化...
log
[ 2025-08-11T14:43:38+08:00 ][ error ] 收到消息：dz/pi/getstatus/710001
error
[ 2025-08-11T14:43:38+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710001',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710001","SIMID":"89861118167312012425","CS":"0","LS":"0","SS":"0","BS":"11.4","RSSI":21,"MT":"4","NG":"0"}',
  'timestamp' => '2025-08-11 14:43:38',
)
error
[ 2025-08-11T14:43:38+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T14:43:38+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710001',
  'SIMID' => '89861118167312012425',
  'CS' => '0',
  'LS' => '0',
  'SS' => '0',
  'BS' => '11.4',
  'RSSI' => 21,
  'MT' => '4',
  'NG' => '0',
)
error
[ 2025-08-11T14:43:38+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710001
error
[ 2025-08-11T14:43:38+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-11T14:43:38+08:00 ][ log ] 【状态上报】开始处理地锁 '710001' 的通用状态...
log
[ 2025-08-11T14:43:38+08:00 ][ log ] 【状态上报】成功更新地锁 '710001' 的快照状态到数据库
log
[ 2025-08-11T14:43:38+08:00 ][ log ] 【状态上报】成功记录地锁 '710001' 的状态到日志表
log
[ 2025-08-11T14:43:38+08:00 ][ log ] 【业务触发】检查 '710001' 的状态变化...
log
[ 2025-08-11T14:43:38+08:00 ][ log ] 【业务触发】检测到地锁 '710001' 开锁成功，结束处理订单...
log
[ 2025-08-11T14:43:38+08:00 ][ log ] 打印order
log
[ 2025-08-11T14:43:38+08:00 ][ log ] array (
  'id' => 60,
  'platform_id' => 20,
  'agent_id' => 82,
  'hospital_id' => 48,
  'hospital_fcbl' => 20.0,
  'hospital_price' => '1.00',
  'hospital_hourlong' => 1,
  'hospital_freedt' => 999999,
  'departments_id' => 34,
  'equipment_id' => 2,
  'equipment_info_id' => 0,
  'info' => '总平台-深圳代理商-南山物业-物业北车棚-设备710001-710001',
  'sn' => 'ord2025081114164117495851',
  'user_id' => 5615,
  'money' => '0.00',
  'status' => '1',
  'pay_types' => '0',
  'pay_status' => '0',
  'pay_time' => NULL,
  'createtime' => 1754893981,
  'returntime' => NULL,
  'updatetime' => 1754893001,
  'is_branch' => '1',
  'admin_info' => NULL,
  'timelong' => 0,
  'timelong_fenzhong' => 0,
  'really_money' => '0.00',
  'actreturntime' => NULL,
  'answer_return_time' => 1754924400,
  'overtime' => NULL,
  'overtime_money' => NULL,
  'normal_money' => NULL,
  'charging_rule' => 1,
  'use_status' => 2,
  'nb_goodsid' => '',
)
log
[ 2025-08-11T14:43:38+08:00 ][ error ] 【业务触发】订单(SN:ord2025081114164117495851)结束逻辑处理成功
error
[ 2025-08-11T14:43:38+08:00 ][ error ] 收到消息：dz/pi/setack/710001
error
[ 2025-08-11T14:43:38+08:00 ][ error ] array (
  'topic' => 'dz/pi/setack/710001',
  'message' => '{"VER":"0","CMD":"17","CD":"710001","CL":"0","PACKID":29857,"STATUS":"1"}',
  'timestamp' => '2025-08-11 14:43:38',
)
error
[ 2025-08-11T14:43:38+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T14:43:38+08:00 ][ error ] array (
  'VER' => '0',
  'CMD' => '17',
  'CD' => '710001',
  'CL' => '0',
  'PACKID' => 29857,
  'STATUS' => '1',
)
error
[ 2025-08-11T14:43:38+08:00 ][ error ] 收到命令响应消息，待处理: dz/pi/setack/710001
error
[ 2025-08-11T14:43:38+08:00 ][ log ] 【命令响应】收到针对命令包 '29857' 的ACK确认
log
[ 2025-08-11T14:43:38+08:00 ][ log ] 【命令响应】成功更新命令 '29857' 的状态为 'acked'
log
[ 2025-08-11T14:44:10+08:00 ][ error ] 收到消息：dz/pi/getstatus/710001
error
[ 2025-08-11T14:44:10+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710001',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710001","SIMID":"89861118167312012425","CS":"0","LS":"1","SS":"0","BS":"10.9","RSSI":21,"MT":"4","NG":"0"}',
  'timestamp' => '2025-08-11 14:44:10',
)
error
[ 2025-08-11T14:44:10+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T14:44:10+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710001',
  'SIMID' => '89861118167312012425',
  'CS' => '0',
  'LS' => '1',
  'SS' => '0',
  'BS' => '10.9',
  'RSSI' => 21,
  'MT' => '4',
  'NG' => '0',
)
error
[ 2025-08-11T14:44:10+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710001
error
[ 2025-08-11T14:44:10+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-11T14:44:10+08:00 ][ log ] 【状态上报】开始处理地锁 '710001' 的通用状态...
log
[ 2025-08-11T14:44:10+08:00 ][ log ] 【状态上报】成功更新地锁 '710001' 的快照状态到数据库
log
[ 2025-08-11T14:44:10+08:00 ][ log ] 【状态上报】成功记录地锁 '710001' 的状态到日志表
log
[ 2025-08-11T14:44:10+08:00 ][ log ] 【业务触发】检查 '710001' 的状态变化...
log
[ 2025-08-11T14:44:10+08:00 ][ log ] 【业务触发】检测到地锁 '710001' 关锁成功，开始处理订单...
log
[ 2025-08-11T14:44:10+08:00 ][ error ] 收到消息：dz/pi/setack/710001
error
[ 2025-08-11T14:44:10+08:00 ][ error ] array (
  'topic' => 'dz/pi/setack/710001',
  'message' => '{"VER":"0","CMD":"17","CD":"710001","CL":"1","PACKID":20861,"STATUS":"81"}',
  'timestamp' => '2025-08-11 14:44:10',
)
error
[ 2025-08-11T14:44:10+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T14:44:10+08:00 ][ error ] array (
  'VER' => '0',
  'CMD' => '17',
  'CD' => '710001',
  'CL' => '1',
  'PACKID' => 20861,
  'STATUS' => '81',
)
error
[ 2025-08-11T14:44:10+08:00 ][ error ] 收到命令响应消息，待处理: dz/pi/setack/710001
error
[ 2025-08-11T14:44:10+08:00 ][ log ] 【命令响应】收到针对命令包 '20861' 的ACK确认
log
[ 2025-08-11T14:44:10+08:00 ][ log ] 【命令响应】成功更新命令 '20861' 的状态为 'acked'
log
[ 2025-08-11T14:44:28+08:00 ][ error ] 收到消息：dz/pi/getstatus/710001
error
[ 2025-08-11T14:44:28+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710001',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710001","SIMID":"89861118167312012425","CS":"0","LS":"0","SS":"0","BS":"11.4","RSSI":21,"MT":"4","NG":"0"}',
  'timestamp' => '2025-08-11 14:44:28',
)
error
[ 2025-08-11T14:44:28+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T14:44:28+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710001',
  'SIMID' => '89861118167312012425',
  'CS' => '0',
  'LS' => '0',
  'SS' => '0',
  'BS' => '11.4',
  'RSSI' => 21,
  'MT' => '4',
  'NG' => '0',
)
error
[ 2025-08-11T14:44:28+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710001
error
[ 2025-08-11T14:44:28+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-11T14:44:28+08:00 ][ log ] 【状态上报】开始处理地锁 '710001' 的通用状态...
log
[ 2025-08-11T14:44:28+08:00 ][ log ] 【状态上报】成功更新地锁 '710001' 的快照状态到数据库
log
[ 2025-08-11T14:44:28+08:00 ][ log ] 【状态上报】成功记录地锁 '710001' 的状态到日志表
log
[ 2025-08-11T14:44:28+08:00 ][ log ] 【业务触发】检查 '710001' 的状态变化...
log
[ 2025-08-11T14:44:28+08:00 ][ log ] 【业务触发】检测到地锁 '710001' 开锁成功，结束处理订单...
log
[ 2025-08-11T14:44:28+08:00 ][ log ] 打印order
log
[ 2025-08-11T14:44:28+08:00 ][ log ] array (
  'id' => 61,
  'platform_id' => 20,
  'agent_id' => 82,
  'hospital_id' => 48,
  'hospital_fcbl' => 20.0,
  'hospital_price' => '1.00',
  'hospital_hourlong' => 1,
  'hospital_freedt' => 999999,
  'departments_id' => 34,
  'equipment_id' => 2,
  'equipment_info_id' => 0,
  'info' => '总平台-深圳代理商-南山物业-物业北车棚-设备710001-710001',
  'sn' => 'ord2025081114440628398755',
  'user_id' => 5615,
  'money' => '0.00',
  'status' => '1',
  'pay_types' => '0',
  'pay_status' => '0',
  'pay_time' => NULL,
  'createtime' => 1754894650,
  'returntime' => NULL,
  'updatetime' => 1754894646,
  'is_branch' => '1',
  'admin_info' => NULL,
  'timelong' => 0,
  'timelong_fenzhong' => 0,
  'really_money' => '0.00',
  'actreturntime' => NULL,
  'answer_return_time' => 1754924400,
  'overtime' => NULL,
  'overtime_money' => NULL,
  'normal_money' => NULL,
  'charging_rule' => 1,
  'use_status' => 2,
  'nb_goodsid' => '',
)
log
[ 2025-08-11T14:44:28+08:00 ][ error ] 【业务触发】订单(SN:ord2025081114440628398755)结束逻辑处理成功
error
[ 2025-08-11T14:44:28+08:00 ][ error ] 收到消息：dz/pi/setack/710001
error
[ 2025-08-11T14:44:28+08:00 ][ error ] array (
  'topic' => 'dz/pi/setack/710001',
  'message' => '{"VER":"0","CMD":"17","CD":"710001","CL":"0","PACKID":48202,"STATUS":"1"}',
  'timestamp' => '2025-08-11 14:44:28',
)
error
[ 2025-08-11T14:44:28+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T14:44:28+08:00 ][ error ] array (
  'VER' => '0',
  'CMD' => '17',
  'CD' => '710001',
  'CL' => '0',
  'PACKID' => 48202,
  'STATUS' => '1',
)
error
[ 2025-08-11T14:44:28+08:00 ][ error ] 收到命令响应消息，待处理: dz/pi/setack/710001
error
[ 2025-08-11T14:44:28+08:00 ][ log ] 【命令响应】收到针对命令包 '48202' 的ACK确认
log
[ 2025-08-11T14:44:28+08:00 ][ log ] 【命令响应】成功更新命令 '48202' 的状态为 'acked'
log
[ 2025-08-11T14:44:54+08:00 ][ error ] 收到消息：dz/pi/getstatus/710001
error
[ 2025-08-11T14:44:54+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710001',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710001","SIMID":"89861118167312012425","CS":"0","LS":"1","SS":"0","BS":"10.2","RSSI":21,"MT":"4","NG":"0"}',
  'timestamp' => '2025-08-11 14:44:54',
)
error
[ 2025-08-11T14:44:54+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T14:44:54+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710001',
  'SIMID' => '89861118167312012425',
  'CS' => '0',
  'LS' => '1',
  'SS' => '0',
  'BS' => '10.2',
  'RSSI' => 21,
  'MT' => '4',
  'NG' => '0',
)
error
[ 2025-08-11T14:44:54+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710001
error
[ 2025-08-11T14:44:54+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-11T14:44:54+08:00 ][ log ] 【状态上报】开始处理地锁 '710001' 的通用状态...
log
[ 2025-08-11T14:44:54+08:00 ][ log ] 【状态上报】成功更新地锁 '710001' 的快照状态到数据库
log
[ 2025-08-11T14:44:54+08:00 ][ log ] 【状态上报】成功记录地锁 '710001' 的状态到日志表
log
[ 2025-08-11T14:44:54+08:00 ][ log ] 【业务触发】检查 '710001' 的状态变化...
log
[ 2025-08-11T14:44:54+08:00 ][ log ] 【业务触发】检测到地锁 '710001' 关锁成功，开始处理订单...
log
[ 2025-08-11T14:44:54+08:00 ][ error ] 收到消息：dz/pi/setack/710001
error
[ 2025-08-11T14:44:54+08:00 ][ error ] array (
  'topic' => 'dz/pi/setack/710001',
  'message' => '{"VER":"0","CMD":"17","CD":"710001","CL":"1","PACKID":14966,"STATUS":"81"}',
  'timestamp' => '2025-08-11 14:44:54',
)
error
[ 2025-08-11T14:44:54+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T14:44:54+08:00 ][ error ] array (
  'VER' => '0',
  'CMD' => '17',
  'CD' => '710001',
  'CL' => '1',
  'PACKID' => 14966,
  'STATUS' => '81',
)
error
[ 2025-08-11T14:44:54+08:00 ][ error ] 收到命令响应消息，待处理: dz/pi/setack/710001
error
[ 2025-08-11T14:44:54+08:00 ][ log ] 【命令响应】收到针对命令包 '14966' 的ACK确认
log
[ 2025-08-11T14:44:54+08:00 ][ log ] 【命令响应】成功更新命令 '14966' 的状态为 'acked'
log
[ 2025-08-11T14:45:05+08:00 ][ error ] 收到消息：dz/pi/getstatus/710001
error
[ 2025-08-11T14:45:05+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710001',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710001","SIMID":"89861118167312012425","CS":"0","LS":"0","SS":"0","BS":"11.4","RSSI":21,"MT":"4","NG":"0"}',
  'timestamp' => '2025-08-11 14:45:05',
)
error
[ 2025-08-11T14:45:05+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T14:45:05+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710001',
  'SIMID' => '89861118167312012425',
  'CS' => '0',
  'LS' => '0',
  'SS' => '0',
  'BS' => '11.4',
  'RSSI' => 21,
  'MT' => '4',
  'NG' => '0',
)
error
[ 2025-08-11T14:45:05+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710001
error
[ 2025-08-11T14:45:05+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-11T14:45:05+08:00 ][ log ] 【状态上报】开始处理地锁 '710001' 的通用状态...
log
[ 2025-08-11T14:45:05+08:00 ][ log ] 【状态上报】成功更新地锁 '710001' 的快照状态到数据库
log
[ 2025-08-11T14:45:05+08:00 ][ log ] 【状态上报】成功记录地锁 '710001' 的状态到日志表
log
[ 2025-08-11T14:45:05+08:00 ][ log ] 【业务触发】检查 '710001' 的状态变化...
log
[ 2025-08-11T14:45:05+08:00 ][ log ] 【业务触发】检测到地锁 '710001' 开锁成功，结束处理订单...
log
[ 2025-08-11T14:45:05+08:00 ][ log ] 打印order
log
[ 2025-08-11T14:45:05+08:00 ][ log ] array (
  'id' => 62,
  'platform_id' => 20,
  'agent_id' => 82,
  'hospital_id' => 48,
  'hospital_fcbl' => 20.0,
  'hospital_price' => '1.00',
  'hospital_hourlong' => 1,
  'hospital_freedt' => 999999,
  'departments_id' => 34,
  'equipment_id' => 2,
  'equipment_info_id' => 0,
  'info' => '总平台-深圳代理商-南山物业-物业北车棚-设备710001-710001',
  'sn' => 'ord2025081114445075398849',
  'user_id' => 5615,
  'money' => '0.00',
  'status' => '1',
  'pay_types' => '0',
  'pay_status' => '0',
  'pay_time' => NULL,
  'createtime' => 1754894694,
  'returntime' => NULL,
  'updatetime' => 1754894690,
  'is_branch' => '1',
  'admin_info' => NULL,
  'timelong' => 0,
  'timelong_fenzhong' => 0,
  'really_money' => '0.00',
  'actreturntime' => NULL,
  'answer_return_time' => 1754924400,
  'overtime' => NULL,
  'overtime_money' => NULL,
  'normal_money' => NULL,
  'charging_rule' => 1,
  'use_status' => 2,
  'nb_goodsid' => '',
)
log
[ 2025-08-11T14:45:05+08:00 ][ error ] 【业务触发】订单(SN:ord2025081114445075398849)结束逻辑处理成功
error
[ 2025-08-11T14:45:05+08:00 ][ error ] 收到消息：dz/pi/setack/710001
error
[ 2025-08-11T14:45:05+08:00 ][ error ] array (
  'topic' => 'dz/pi/setack/710001',
  'message' => '{"VER":"0","CMD":"17","CD":"710001","CL":"0","PACKID":37428,"STATUS":"1"}',
  'timestamp' => '2025-08-11 14:45:05',
)
error
[ 2025-08-11T14:45:05+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T14:45:05+08:00 ][ error ] array (
  'VER' => '0',
  'CMD' => '17',
  'CD' => '710001',
  'CL' => '0',
  'PACKID' => 37428,
  'STATUS' => '1',
)
error
[ 2025-08-11T14:45:05+08:00 ][ error ] 收到命令响应消息，待处理: dz/pi/setack/710001
error
[ 2025-08-11T14:45:05+08:00 ][ log ] 【命令响应】收到针对命令包 '37428' 的ACK确认
log
[ 2025-08-11T14:45:05+08:00 ][ log ] 【命令响应】成功更新命令 '37428' 的状态为 'acked'
log
[ 2025-08-11T14:49:14+08:00 ][ error ] 收到消息：dz/pi/getstatus/710001
error
[ 2025-08-11T14:49:14+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710001',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710001","SIMID":"89861118167312012425","CS":"0","LS":"1","SS":"0","BS":"10.3","RSSI":21,"MT":"4","NG":"0"}',
  'timestamp' => '2025-08-11 14:49:14',
)
error
[ 2025-08-11T14:49:14+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T14:49:14+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710001',
  'SIMID' => '89861118167312012425',
  'CS' => '0',
  'LS' => '1',
  'SS' => '0',
  'BS' => '10.3',
  'RSSI' => 21,
  'MT' => '4',
  'NG' => '0',
)
error
[ 2025-08-11T14:49:14+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710001
error
[ 2025-08-11T14:49:14+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-11T14:49:14+08:00 ][ log ] 【状态上报】开始处理地锁 '710001' 的通用状态...
log
[ 2025-08-11T14:49:14+08:00 ][ log ] 【状态上报】成功更新地锁 '710001' 的快照状态到数据库
log
[ 2025-08-11T14:49:14+08:00 ][ log ] 【状态上报】成功记录地锁 '710001' 的状态到日志表
log
[ 2025-08-11T14:49:14+08:00 ][ log ] 【业务触发】检查 '710001' 的状态变化...
log
[ 2025-08-11T14:49:14+08:00 ][ log ] 【业务触发】检测到地锁 '710001' 关锁成功，开始处理订单...
log
[ 2025-08-11T14:49:14+08:00 ][ error ] 收到消息：dz/pi/setack/710001
error
[ 2025-08-11T14:49:14+08:00 ][ error ] array (
  'topic' => 'dz/pi/setack/710001',
  'message' => '{"VER":"0","CMD":"17","CD":"710001","CL":"1","PACKID":54054,"STATUS":"81"}',
  'timestamp' => '2025-08-11 14:49:14',
)
error
[ 2025-08-11T14:49:14+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T14:49:14+08:00 ][ error ] array (
  'VER' => '0',
  'CMD' => '17',
  'CD' => '710001',
  'CL' => '1',
  'PACKID' => 54054,
  'STATUS' => '81',
)
error
[ 2025-08-11T14:49:14+08:00 ][ error ] 收到命令响应消息，待处理: dz/pi/setack/710001
error
[ 2025-08-11T14:49:14+08:00 ][ log ] 【命令响应】收到针对命令包 '54054' 的ACK确认
log
[ 2025-08-11T14:49:14+08:00 ][ log ] 【命令响应】成功更新命令 '54054' 的状态为 'acked'
log
[ 2025-08-11T14:50:28+08:00 ][ error ] 收到消息：dz/pi/getstatus/710001
error
[ 2025-08-11T14:50:28+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710001',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710001","SIMID":"89861118167312012425","CS":"0","LS":"0","SS":"0","BS":"11.3","RSSI":21,"MT":"4","NG":"0"}',
  'timestamp' => '2025-08-11 14:50:28',
)
error
[ 2025-08-11T14:50:28+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T14:50:28+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710001',
  'SIMID' => '89861118167312012425',
  'CS' => '0',
  'LS' => '0',
  'SS' => '0',
  'BS' => '11.3',
  'RSSI' => 21,
  'MT' => '4',
  'NG' => '0',
)
error
[ 2025-08-11T14:50:28+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710001
error
[ 2025-08-11T14:50:28+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-11T14:50:28+08:00 ][ log ] 【状态上报】开始处理地锁 '710001' 的通用状态...
log
[ 2025-08-11T14:50:28+08:00 ][ log ] 【状态上报】成功更新地锁 '710001' 的快照状态到数据库
log
[ 2025-08-11T14:50:28+08:00 ][ log ] 【状态上报】成功记录地锁 '710001' 的状态到日志表
log
[ 2025-08-11T14:50:28+08:00 ][ log ] 【业务触发】检查 '710001' 的状态变化...
log
[ 2025-08-11T14:50:28+08:00 ][ log ] 【业务触发】检测到地锁 '710001' 开锁成功，结束处理订单...
log
[ 2025-08-11T14:50:28+08:00 ][ log ] 打印order
log
[ 2025-08-11T14:50:28+08:00 ][ log ] array (
  'id' => 63,
  'platform_id' => 20,
  'agent_id' => 82,
  'hospital_id' => 48,
  'hospital_fcbl' => 20.0,
  'hospital_price' => '1.00',
  'hospital_hourlong' => 1,
  'hospital_freedt' => 999999,
  'departments_id' => 34,
  'equipment_id' => 2,
  'equipment_info_id' => 0,
  'info' => '总平台-深圳代理商-南山物业-物业北车棚-设备710001-710001',
  'sn' => 'ord2025081114490936936099',
  'user_id' => 5612,
  'money' => '0.00',
  'status' => '1',
  'pay_types' => '0',
  'pay_status' => '0',
  'pay_time' => NULL,
  'createtime' => 1754894954,
  'returntime' => NULL,
  'updatetime' => 1754894949,
  'is_branch' => '1',
  'admin_info' => NULL,
  'timelong' => 0,
  'timelong_fenzhong' => 0,
  'really_money' => '0.00',
  'actreturntime' => NULL,
  'answer_return_time' => 1754924400,
  'overtime' => NULL,
  'overtime_money' => NULL,
  'normal_money' => NULL,
  'charging_rule' => 1,
  'use_status' => 2,
  'nb_goodsid' => '',
)
log
[ 2025-08-11T14:50:28+08:00 ][ error ] 【业务触发】订单(SN:ord2025081114490936936099)结束逻辑处理成功
error
[ 2025-08-11T14:50:28+08:00 ][ error ] 收到消息：dz/pi/setack/710001
error
[ 2025-08-11T14:50:28+08:00 ][ error ] array (
  'topic' => 'dz/pi/setack/710001',
  'message' => '{"VER":"0","CMD":"17","CD":"710001","CL":"0","PACKID":25884,"STATUS":"1"}',
  'timestamp' => '2025-08-11 14:50:28',
)
error
[ 2025-08-11T14:50:28+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T14:50:28+08:00 ][ error ] array (
  'VER' => '0',
  'CMD' => '17',
  'CD' => '710001',
  'CL' => '0',
  'PACKID' => 25884,
  'STATUS' => '1',
)
error
[ 2025-08-11T14:50:28+08:00 ][ error ] 收到命令响应消息，待处理: dz/pi/setack/710001
error
[ 2025-08-11T14:50:28+08:00 ][ log ] 【命令响应】收到针对命令包 '25884' 的ACK确认
log
[ 2025-08-11T14:50:28+08:00 ][ log ] 【命令响应】成功更新命令 '25884' 的状态为 'acked'
log
[ 2025-08-11T15:00:27+08:00 ][ error ] 收到消息：dz/pi/getstatus/710001
error
[ 2025-08-11T15:00:27+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710001',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710001","SIMID":"89861118167312012425","CS":"0","LS":"0","SS":"0","BS":"12.3","RSSI":21,"MT":"20","NG":"0"}',
  'timestamp' => '2025-08-11 15:00:27',
)
error
[ 2025-08-11T15:00:27+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T15:00:27+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710001',
  'SIMID' => '89861118167312012425',
  'CS' => '0',
  'LS' => '0',
  'SS' => '0',
  'BS' => '12.3',
  'RSSI' => 21,
  'MT' => '20',
  'NG' => '0',
)
error
[ 2025-08-11T15:00:27+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710001
error
[ 2025-08-11T15:00:27+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-11T15:00:27+08:00 ][ log ] 【状态上报】开始处理地锁 '710001' 的通用状态...
log
[ 2025-08-11T15:00:27+08:00 ][ log ] 【状态上报】成功更新地锁 '710001' 的快照状态到数据库
log
[ 2025-08-11T15:00:27+08:00 ][ log ] 【状态上报】成功记录地锁 '710001' 的状态到日志表
log
[ 2025-08-11T15:00:27+08:00 ][ log ] 【业务触发】检查 '710001' 的状态变化...
log
[ 2025-08-11T15:04:06+08:00 ][ error ] 收到消息：dz/pi/getstatus/710001
error
[ 2025-08-11T15:04:06+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710001',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710001","SIMID":"89861118167312012425","CS":"0","LS":"1","SS":"0","BS":"10.6","RSSI":21,"MT":"4","NG":"0"}',
  'timestamp' => '2025-08-11 15:04:06',
)
error
[ 2025-08-11T15:04:06+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T15:04:06+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710001',
  'SIMID' => '89861118167312012425',
  'CS' => '0',
  'LS' => '1',
  'SS' => '0',
  'BS' => '10.6',
  'RSSI' => 21,
  'MT' => '4',
  'NG' => '0',
)
error
[ 2025-08-11T15:04:06+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710001
error
[ 2025-08-11T15:04:06+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-11T15:04:06+08:00 ][ log ] 【状态上报】开始处理地锁 '710001' 的通用状态...
log
[ 2025-08-11T15:04:06+08:00 ][ log ] 【状态上报】成功更新地锁 '710001' 的快照状态到数据库
log
[ 2025-08-11T15:04:06+08:00 ][ log ] 【状态上报】成功记录地锁 '710001' 的状态到日志表
log
[ 2025-08-11T15:04:06+08:00 ][ log ] 【业务触发】检查 '710001' 的状态变化...
log
[ 2025-08-11T15:04:06+08:00 ][ log ] 【业务触发】检测到地锁 '710001' 关锁成功，开始处理订单...
log
[ 2025-08-11T15:04:06+08:00 ][ error ] 收到消息：dz/pi/setack/710001
error
[ 2025-08-11T15:04:06+08:00 ][ error ] array (
  'topic' => 'dz/pi/setack/710001',
  'message' => '{"VER":"0","CMD":"17","CD":"710001","CL":"1","PACKID":15109,"STATUS":"81"}',
  'timestamp' => '2025-08-11 15:04:06',
)
error
[ 2025-08-11T15:04:06+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T15:04:06+08:00 ][ error ] array (
  'VER' => '0',
  'CMD' => '17',
  'CD' => '710001',
  'CL' => '1',
  'PACKID' => 15109,
  'STATUS' => '81',
)
error
[ 2025-08-11T15:04:06+08:00 ][ error ] 收到命令响应消息，待处理: dz/pi/setack/710001
error
[ 2025-08-11T15:04:06+08:00 ][ log ] 【命令响应】收到针对命令包 '15109' 的ACK确认
log
[ 2025-08-11T15:04:06+08:00 ][ log ] 【命令响应】成功更新命令 '15109' 的状态为 'acked'
log
[ 2025-08-11T15:04:26+08:00 ][ error ] 收到消息：dz/pi/getstatus/710001
error
[ 2025-08-11T15:04:26+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710001',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710001","SIMID":"89861118167312012425","CS":"0","LS":"0","SS":"0","BS":"11.3","RSSI":21,"MT":"4","NG":"0"}',
  'timestamp' => '2025-08-11 15:04:26',
)
error
[ 2025-08-11T15:04:26+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T15:04:26+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710001',
  'SIMID' => '89861118167312012425',
  'CS' => '0',
  'LS' => '0',
  'SS' => '0',
  'BS' => '11.3',
  'RSSI' => 21,
  'MT' => '4',
  'NG' => '0',
)
error
[ 2025-08-11T15:04:26+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710001
error
[ 2025-08-11T15:04:26+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-11T15:04:26+08:00 ][ log ] 【状态上报】开始处理地锁 '710001' 的通用状态...
log
[ 2025-08-11T15:04:26+08:00 ][ log ] 【状态上报】成功更新地锁 '710001' 的快照状态到数据库
log
[ 2025-08-11T15:04:26+08:00 ][ log ] 【状态上报】成功记录地锁 '710001' 的状态到日志表
log
[ 2025-08-11T15:04:26+08:00 ][ log ] 【业务触发】检查 '710001' 的状态变化...
log
[ 2025-08-11T15:04:26+08:00 ][ log ] 【业务触发】检测到地锁 '710001' 开锁成功，结束处理订单...
log
[ 2025-08-11T15:04:26+08:00 ][ log ] 打印order
log
[ 2025-08-11T15:04:26+08:00 ][ log ] array (
  'id' => 64,
  'platform_id' => 20,
  'agent_id' => 82,
  'hospital_id' => 48,
  'hospital_fcbl' => 20.0,
  'hospital_price' => '1.00',
  'hospital_hourlong' => 1,
  'hospital_freedt' => 999999,
  'departments_id' => 34,
  'equipment_id' => 2,
  'equipment_info_id' => 0,
  'info' => '总平台-深圳代理商-南山物业-物业北车棚-设备710001-710001',
  'sn' => 'ord2025081115040240118464',
  'user_id' => 5615,
  'money' => '0.00',
  'status' => '1',
  'pay_types' => '0',
  'pay_status' => '0',
  'pay_time' => NULL,
  'createtime' => 1754895846,
  'returntime' => NULL,
  'updatetime' => 1754895842,
  'is_branch' => '1',
  'admin_info' => NULL,
  'timelong' => 0,
  'timelong_fenzhong' => 0,
  'really_money' => '0.00',
  'actreturntime' => NULL,
  'answer_return_time' => 1754924400,
  'overtime' => NULL,
  'overtime_money' => NULL,
  'normal_money' => NULL,
  'charging_rule' => 1,
  'use_status' => 2,
  'nb_goodsid' => '',
)
log
[ 2025-08-11T15:04:26+08:00 ][ error ] 【业务触发】订单(SN:ord2025081115040240118464)结束逻辑处理成功
error
[ 2025-08-11T15:04:26+08:00 ][ error ] 收到消息：dz/pi/setack/710001
error
[ 2025-08-11T15:04:26+08:00 ][ error ] array (
  'topic' => 'dz/pi/setack/710001',
  'message' => '{"VER":"0","CMD":"17","CD":"710001","CL":"0","PACKID":51521,"STATUS":"1"}',
  'timestamp' => '2025-08-11 15:04:26',
)
error
[ 2025-08-11T15:04:26+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T15:04:26+08:00 ][ error ] array (
  'VER' => '0',
  'CMD' => '17',
  'CD' => '710001',
  'CL' => '0',
  'PACKID' => 51521,
  'STATUS' => '1',
)
error
[ 2025-08-11T15:04:26+08:00 ][ error ] 收到命令响应消息，待处理: dz/pi/setack/710001
error
[ 2025-08-11T15:04:26+08:00 ][ log ] 【命令响应】收到针对命令包 '51521' 的ACK确认
log
[ 2025-08-11T15:04:26+08:00 ][ log ] 【命令响应】成功更新命令 '51521' 的状态为 'acked'
log
[ 2025-08-11T15:10:30+08:00 ][ error ] 收到消息：dz/pi/mstatus/710002
error
[ 2025-08-11T15:10:30+08:00 ][ error ] array (
  'topic' => 'dz/pi/mstatus/710002',
  'message' => '{"MD":"710002","MS":"1","SS":"28"}',
  'timestamp' => '2025-08-11 15:10:30',
)
error
[ 2025-08-11T15:10:30+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T15:10:30+08:00 ][ error ] array (
  'MD' => '710002',
  'MS' => '1',
  'SS' => '28',
)
error
[ 2025-08-11T15:10:30+08:00 ][ log ] 收到地锁 '710002' 的状态更新: 上线, 信号强度: 28
log
[ 2025-08-11T15:10:30+08:00 ][ log ] 未在数据库中找到SN为 '710002' 的地锁设备
log
[ 2025-08-11T15:10:30+08:00 ][ error ] 收到消息：dz/pi/mstatus/710003
error
[ 2025-08-11T15:10:30+08:00 ][ error ] array (
  'topic' => 'dz/pi/mstatus/710003',
  'message' => '{"MD":"710003","MS":"1","SS":"31"}',
  'timestamp' => '2025-08-11 15:10:30',
)
error
[ 2025-08-11T15:10:30+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T15:10:30+08:00 ][ error ] array (
  'MD' => '710003',
  'MS' => '1',
  'SS' => '31',
)
error
[ 2025-08-11T15:10:30+08:00 ][ log ] 收到地锁 '710003' 的状态更新: 上线, 信号强度: 31
log
[ 2025-08-11T15:10:30+08:00 ][ log ] 未在数据库中找到SN为 '710003' 的地锁设备
log
[ 2025-08-11T15:10:30+08:00 ][ error ] 收到消息：dz/pi/getstatus/710002
error
[ 2025-08-11T15:10:30+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710002',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710002","SIMID":"898604F4152391140826","CS":"0","LS":"0","SS":"0","BS":"12.2","RSSI":28,"MT":"1","NG":"0"}',
  'timestamp' => '2025-08-11 15:10:30',
)
error
[ 2025-08-11T15:10:30+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T15:10:30+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710002',
  'SIMID' => '898604F4152391140826',
  'CS' => '0',
  'LS' => '0',
  'SS' => '0',
  'BS' => '12.2',
  'RSSI' => 28,
  'MT' => '1',
  'NG' => '0',
)
error
[ 2025-08-11T15:10:30+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710002
error
[ 2025-08-11T15:10:30+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-11T15:10:30+08:00 ][ log ] 【状态上报】开始处理地锁 '710002' 的通用状态...
log
[ 2025-08-11T15:10:30+08:00 ][ log ] 【状态上报】未在数据库中找到SN为 '710002' 的地锁设备
log
[ 2025-08-11T15:10:30+08:00 ][ error ] 收到消息：dz/pi/getstatus/710003
error
[ 2025-08-11T15:10:30+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710003',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710003","SIMID":"898604F4152391140839","CS":"0","LS":"0","SS":"0","BS":"12.3","RSSI":31,"MT":"1","NG":"0"}',
  'timestamp' => '2025-08-11 15:10:30',
)
error
[ 2025-08-11T15:10:30+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T15:10:30+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710003',
  'SIMID' => '898604F4152391140839',
  'CS' => '0',
  'LS' => '0',
  'SS' => '0',
  'BS' => '12.3',
  'RSSI' => 31,
  'MT' => '1',
  'NG' => '0',
)
error
[ 2025-08-11T15:10:30+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710003
error
[ 2025-08-11T15:10:30+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-11T15:10:30+08:00 ][ log ] 【状态上报】开始处理地锁 '710003' 的通用状态...
log
[ 2025-08-11T15:10:30+08:00 ][ log ] 【状态上报】未在数据库中找到SN为 '710003' 的地锁设备
log
[ 2025-08-11T15:14:25+08:00 ][ error ] 收到消息：dz/pi/getstatus/710001
error
[ 2025-08-11T15:14:25+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710001',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710001","SIMID":"89861118167312012425","CS":"0","LS":"0","SS":"0","BS":"12.3","RSSI":21,"MT":"20","NG":"0"}',
  'timestamp' => '2025-08-11 15:14:25',
)
error
[ 2025-08-11T15:14:25+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T15:14:25+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710001',
  'SIMID' => '89861118167312012425',
  'CS' => '0',
  'LS' => '0',
  'SS' => '0',
  'BS' => '12.3',
  'RSSI' => 21,
  'MT' => '20',
  'NG' => '0',
)
error
[ 2025-08-11T15:14:25+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710001
error
[ 2025-08-11T15:14:25+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-11T15:14:25+08:00 ][ log ] 【状态上报】开始处理地锁 '710001' 的通用状态...
log
[ 2025-08-11T15:14:25+08:00 ][ log ] 【状态上报】成功更新地锁 '710001' 的快照状态到数据库
log
[ 2025-08-11T15:14:25+08:00 ][ log ] 【状态上报】成功记录地锁 '710001' 的状态到日志表
log
[ 2025-08-11T15:14:25+08:00 ][ log ] 【业务触发】检查 '710001' 的状态变化...
log
[ 2025-08-11T15:16:00+08:00 ][ error ] 收到消息：dz/pi/getstatus/710001
error
[ 2025-08-11T15:16:00+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710001',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710001","SIMID":"89861118167312012425","CS":"0","LS":"1","SS":"0","BS":"10.3","RSSI":21,"MT":"4","NG":"0"}',
  'timestamp' => '2025-08-11 15:16:00',
)
error
[ 2025-08-11T15:16:00+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T15:16:00+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710001',
  'SIMID' => '89861118167312012425',
  'CS' => '0',
  'LS' => '1',
  'SS' => '0',
  'BS' => '10.3',
  'RSSI' => 21,
  'MT' => '4',
  'NG' => '0',
)
error
[ 2025-08-11T15:16:00+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710001
error
[ 2025-08-11T15:16:00+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-11T15:16:00+08:00 ][ log ] 【状态上报】开始处理地锁 '710001' 的通用状态...
log
[ 2025-08-11T15:16:00+08:00 ][ log ] 【状态上报】成功更新地锁 '710001' 的快照状态到数据库
log
[ 2025-08-11T15:16:00+08:00 ][ log ] 【状态上报】成功记录地锁 '710001' 的状态到日志表
log
[ 2025-08-11T15:16:00+08:00 ][ log ] 【业务触发】检查 '710001' 的状态变化...
log
[ 2025-08-11T15:16:00+08:00 ][ log ] 【业务触发】检测到地锁 '710001' 关锁成功，开始处理订单...
log
[ 2025-08-11T15:16:00+08:00 ][ error ] 收到消息：dz/pi/setack/710001
error
[ 2025-08-11T15:16:00+08:00 ][ error ] array (
  'topic' => 'dz/pi/setack/710001',
  'message' => '{"VER":"0","CMD":"17","CD":"710001","CL":"1","PACKID":47977,"STATUS":"81"}',
  'timestamp' => '2025-08-11 15:16:00',
)
error
[ 2025-08-11T15:16:00+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T15:16:00+08:00 ][ error ] array (
  'VER' => '0',
  'CMD' => '17',
  'CD' => '710001',
  'CL' => '1',
  'PACKID' => 47977,
  'STATUS' => '81',
)
error
[ 2025-08-11T15:16:00+08:00 ][ error ] 收到命令响应消息，待处理: dz/pi/setack/710001
error
[ 2025-08-11T15:16:00+08:00 ][ log ] 【命令响应】收到针对命令包 '47977' 的ACK确认
log
[ 2025-08-11T15:16:00+08:00 ][ log ] 【命令响应】成功更新命令 '47977' 的状态为 'acked'
log
[ 2025-08-11T15:16:15+08:00 ][ error ] 收到消息：dz/pi/getstatus/710001
error
[ 2025-08-11T15:16:15+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710001',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710001","SIMID":"89861118167312012425","CS":"0","LS":"0","SS":"0","BS":"11.3","RSSI":21,"MT":"4","NG":"0"}',
  'timestamp' => '2025-08-11 15:16:15',
)
error
[ 2025-08-11T15:16:15+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T15:16:15+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710001',
  'SIMID' => '89861118167312012425',
  'CS' => '0',
  'LS' => '0',
  'SS' => '0',
  'BS' => '11.3',
  'RSSI' => 21,
  'MT' => '4',
  'NG' => '0',
)
error
[ 2025-08-11T15:16:15+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710001
error
[ 2025-08-11T15:16:15+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-11T15:16:15+08:00 ][ log ] 【状态上报】开始处理地锁 '710001' 的通用状态...
log
[ 2025-08-11T15:16:15+08:00 ][ log ] 【状态上报】成功更新地锁 '710001' 的快照状态到数据库
log
[ 2025-08-11T15:16:15+08:00 ][ log ] 【状态上报】成功记录地锁 '710001' 的状态到日志表
log
[ 2025-08-11T15:16:15+08:00 ][ log ] 【业务触发】检查 '710001' 的状态变化...
log
[ 2025-08-11T15:16:15+08:00 ][ log ] 【业务触发】检测到地锁 '710001' 开锁成功，结束处理订单...
log
[ 2025-08-11T15:16:15+08:00 ][ log ] 打印order
log
[ 2025-08-11T15:16:15+08:00 ][ log ] array (
  'id' => 65,
  'platform_id' => 20,
  'agent_id' => 82,
  'hospital_id' => 48,
  'hospital_fcbl' => 20.0,
  'hospital_price' => '1.00',
  'hospital_hourlong' => 1,
  'hospital_freedt' => 999999,
  'departments_id' => 34,
  'equipment_id' => 2,
  'equipment_info_id' => 0,
  'info' => '总平台-深圳代理商-南山物业-物业北车棚-设备710001-710001',
  'sn' => 'ord2025081115155692900051',
  'user_id' => 5611,
  'money' => '0.00',
  'status' => '1',
  'pay_types' => '0',
  'pay_status' => '0',
  'pay_time' => NULL,
  'createtime' => 1754896560,
  'returntime' => NULL,
  'updatetime' => 1754896556,
  'is_branch' => '1',
  'admin_info' => NULL,
  'timelong' => 0,
  'timelong_fenzhong' => 0,
  'really_money' => '0.00',
  'actreturntime' => NULL,
  'answer_return_time' => 1754924400,
  'overtime' => NULL,
  'overtime_money' => NULL,
  'normal_money' => NULL,
  'charging_rule' => 1,
  'use_status' => 2,
  'nb_goodsid' => '',
)
log
[ 2025-08-11T15:16:15+08:00 ][ error ] 【业务触发】订单(SN:ord2025081115155692900051)结束逻辑处理成功
error
[ 2025-08-11T15:16:15+08:00 ][ error ] 收到消息：dz/pi/setack/710001
error
[ 2025-08-11T15:16:15+08:00 ][ error ] array (
  'topic' => 'dz/pi/setack/710001',
  'message' => '{"VER":"0","CMD":"17","CD":"710001","CL":"0","PACKID":42583,"STATUS":"1"}',
  'timestamp' => '2025-08-11 15:16:15',
)
error
[ 2025-08-11T15:16:15+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T15:16:15+08:00 ][ error ] array (
  'VER' => '0',
  'CMD' => '17',
  'CD' => '710001',
  'CL' => '0',
  'PACKID' => 42583,
  'STATUS' => '1',
)
error
[ 2025-08-11T15:16:15+08:00 ][ error ] 收到命令响应消息，待处理: dz/pi/setack/710001
error
[ 2025-08-11T15:16:15+08:00 ][ log ] 【命令响应】收到针对命令包 '42583' 的ACK确认
log
[ 2025-08-11T15:16:15+08:00 ][ log ] 【命令响应】成功更新命令 '42583' 的状态为 'acked'
log
[ 2025-08-11T15:18:22+08:00 ][ error ] 收到消息：dz/pi/getstatus/710001
error
[ 2025-08-11T15:18:22+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710001',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710001","SIMID":"89861118167312012425","CS":"0","LS":"1","SS":"0","BS":"10.1","RSSI":21,"MT":"4","NG":"0"}',
  'timestamp' => '2025-08-11 15:18:22',
)
error
[ 2025-08-11T15:18:22+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T15:18:22+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710001',
  'SIMID' => '89861118167312012425',
  'CS' => '0',
  'LS' => '1',
  'SS' => '0',
  'BS' => '10.1',
  'RSSI' => 21,
  'MT' => '4',
  'NG' => '0',
)
error
[ 2025-08-11T15:18:22+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710001
error
[ 2025-08-11T15:18:22+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-11T15:18:22+08:00 ][ log ] 【状态上报】开始处理地锁 '710001' 的通用状态...
log
[ 2025-08-11T15:18:22+08:00 ][ log ] 【状态上报】成功更新地锁 '710001' 的快照状态到数据库
log
[ 2025-08-11T15:18:22+08:00 ][ log ] 【状态上报】成功记录地锁 '710001' 的状态到日志表
log
[ 2025-08-11T15:18:22+08:00 ][ log ] 【业务触发】检查 '710001' 的状态变化...
log
[ 2025-08-11T15:18:22+08:00 ][ log ] 【业务触发】检测到地锁 '710001' 关锁成功，开始处理订单...
log
[ 2025-08-11T15:18:23+08:00 ][ error ] 收到消息：dz/pi/setack/710001
error
[ 2025-08-11T15:18:23+08:00 ][ error ] array (
  'topic' => 'dz/pi/setack/710001',
  'message' => '{"VER":"0","CMD":"17","CD":"710001","CL":"1","PACKID":47973,"STATUS":"81"}',
  'timestamp' => '2025-08-11 15:18:23',
)
error
[ 2025-08-11T15:18:23+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T15:18:23+08:00 ][ error ] array (
  'VER' => '0',
  'CMD' => '17',
  'CD' => '710001',
  'CL' => '1',
  'PACKID' => 47973,
  'STATUS' => '81',
)
error
[ 2025-08-11T15:18:23+08:00 ][ error ] 收到命令响应消息，待处理: dz/pi/setack/710001
error
[ 2025-08-11T15:18:23+08:00 ][ log ] 【命令响应】收到针对命令包 '47973' 的ACK确认
log
[ 2025-08-11T15:18:23+08:00 ][ log ] 【命令响应】成功更新命令 '47973' 的状态为 'acked'
log
[ 2025-08-11T15:19:44+08:00 ][ error ] 收到消息：dz/pi/getstatus/710001
error
[ 2025-08-11T15:19:44+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710001',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710001","SIMID":"89861118167312012425","CS":"0","LS":"0","SS":"0","BS":"11.4","RSSI":21,"MT":"4","NG":"0"}',
  'timestamp' => '2025-08-11 15:19:44',
)
error
[ 2025-08-11T15:19:44+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T15:19:44+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710001',
  'SIMID' => '89861118167312012425',
  'CS' => '0',
  'LS' => '0',
  'SS' => '0',
  'BS' => '11.4',
  'RSSI' => 21,
  'MT' => '4',
  'NG' => '0',
)
error
[ 2025-08-11T15:19:44+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710001
error
[ 2025-08-11T15:19:44+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-11T15:19:44+08:00 ][ log ] 【状态上报】开始处理地锁 '710001' 的通用状态...
log
[ 2025-08-11T15:19:44+08:00 ][ log ] 【状态上报】成功更新地锁 '710001' 的快照状态到数据库
log
[ 2025-08-11T15:19:44+08:00 ][ log ] 【状态上报】成功记录地锁 '710001' 的状态到日志表
log
[ 2025-08-11T15:19:44+08:00 ][ log ] 【业务触发】检查 '710001' 的状态变化...
log
[ 2025-08-11T15:19:44+08:00 ][ log ] 【业务触发】检测到地锁 '710001' 开锁成功，结束处理订单...
log
[ 2025-08-11T15:19:44+08:00 ][ log ] 打印order
log
[ 2025-08-11T15:19:44+08:00 ][ log ] array (
  'id' => 66,
  'platform_id' => 20,
  'agent_id' => 82,
  'hospital_id' => 48,
  'hospital_fcbl' => 20.0,
  'hospital_price' => '1.00',
  'hospital_hourlong' => 1,
  'hospital_freedt' => 999999,
  'departments_id' => 34,
  'equipment_id' => 2,
  'equipment_info_id' => 0,
  'info' => '总平台-深圳代理商-南山物业-物业北车棚-设备710001-710001',
  'sn' => 'ord2025081115181865301964',
  'user_id' => 5611,
  'money' => '0.00',
  'status' => '1',
  'pay_types' => '0',
  'pay_status' => '0',
  'pay_time' => NULL,
  'createtime' => 1754896702,
  'returntime' => NULL,
  'updatetime' => 1754896698,
  'is_branch' => '1',
  'admin_info' => NULL,
  'timelong' => 0,
  'timelong_fenzhong' => 0,
  'really_money' => '0.00',
  'actreturntime' => NULL,
  'answer_return_time' => 1754924400,
  'overtime' => NULL,
  'overtime_money' => NULL,
  'normal_money' => NULL,
  'charging_rule' => 1,
  'use_status' => 2,
  'nb_goodsid' => '',
)
log
[ 2025-08-11T15:19:44+08:00 ][ error ] 【业务触发】订单(SN:ord2025081115181865301964)结束逻辑处理成功
error
[ 2025-08-11T15:19:44+08:00 ][ error ] 收到消息：dz/pi/setack/710001
error
[ 2025-08-11T15:19:44+08:00 ][ error ] array (
  'topic' => 'dz/pi/setack/710001',
  'message' => '{"VER":"0","CMD":"17","CD":"710001","CL":"0","PACKID":14854,"STATUS":"1"}',
  'timestamp' => '2025-08-11 15:19:44',
)
error
[ 2025-08-11T15:19:44+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T15:19:44+08:00 ][ error ] array (
  'VER' => '0',
  'CMD' => '17',
  'CD' => '710001',
  'CL' => '0',
  'PACKID' => 14854,
  'STATUS' => '1',
)
error
[ 2025-08-11T15:19:44+08:00 ][ error ] 收到命令响应消息，待处理: dz/pi/setack/710001
error
[ 2025-08-11T15:19:44+08:00 ][ log ] 【命令响应】收到针对命令包 '14854' 的ACK确认
log
[ 2025-08-11T15:19:44+08:00 ][ log ] 【命令响应】成功更新命令 '14854' 的状态为 'acked'
log
[ 2025-08-11T15:20:02+08:00 ][ error ] 收到消息：dz/pi/getstatus/710001
error
[ 2025-08-11T15:20:02+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710001',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710001","SIMID":"89861118167312012425","CS":"0","LS":"1","SS":"0","BS":"10.3","RSSI":21,"MT":"4","NG":"0"}',
  'timestamp' => '2025-08-11 15:20:02',
)
error
[ 2025-08-11T15:20:02+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T15:20:02+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710001',
  'SIMID' => '89861118167312012425',
  'CS' => '0',
  'LS' => '1',
  'SS' => '0',
  'BS' => '10.3',
  'RSSI' => 21,
  'MT' => '4',
  'NG' => '0',
)
error
[ 2025-08-11T15:20:02+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710001
error
[ 2025-08-11T15:20:02+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-11T15:20:02+08:00 ][ log ] 【状态上报】开始处理地锁 '710001' 的通用状态...
log
[ 2025-08-11T15:20:02+08:00 ][ log ] 【状态上报】成功更新地锁 '710001' 的快照状态到数据库
log
[ 2025-08-11T15:20:02+08:00 ][ log ] 【状态上报】成功记录地锁 '710001' 的状态到日志表
log
[ 2025-08-11T15:20:02+08:00 ][ log ] 【业务触发】检查 '710001' 的状态变化...
log
[ 2025-08-11T15:20:02+08:00 ][ log ] 【业务触发】检测到地锁 '710001' 关锁成功，开始处理订单...
log
[ 2025-08-11T15:20:02+08:00 ][ error ] 收到消息：dz/pi/setack/710001
error
[ 2025-08-11T15:20:02+08:00 ][ error ] array (
  'topic' => 'dz/pi/setack/710001',
  'message' => '{"VER":"0","CMD":"17","CD":"710001","CL":"1","PACKID":163,"STATUS":"81"}',
  'timestamp' => '2025-08-11 15:20:02',
)
error
[ 2025-08-11T15:20:02+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T15:20:02+08:00 ][ error ] array (
  'VER' => '0',
  'CMD' => '17',
  'CD' => '710001',
  'CL' => '1',
  'PACKID' => 163,
  'STATUS' => '81',
)
error
[ 2025-08-11T15:20:02+08:00 ][ error ] 收到命令响应消息，待处理: dz/pi/setack/710001
error
[ 2025-08-11T15:20:02+08:00 ][ log ] 【命令响应】收到针对命令包 '163' 的ACK确认
log
[ 2025-08-11T15:20:02+08:00 ][ log ] 【命令响应】成功更新命令 '163' 的状态为 'acked'
log
[ 2025-08-11T15:20:22+08:00 ][ error ] 收到消息：dz/pi/getstatus/710001
error
[ 2025-08-11T15:20:22+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710001',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710001","SIMID":"89861118167312012425","CS":"0","LS":"0","SS":"0","BS":"11.3","RSSI":21,"MT":"4","NG":"0"}',
  'timestamp' => '2025-08-11 15:20:22',
)
error
[ 2025-08-11T15:20:22+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T15:20:22+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710001',
  'SIMID' => '89861118167312012425',
  'CS' => '0',
  'LS' => '0',
  'SS' => '0',
  'BS' => '11.3',
  'RSSI' => 21,
  'MT' => '4',
  'NG' => '0',
)
error
[ 2025-08-11T15:20:22+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710001
error
[ 2025-08-11T15:20:22+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-11T15:20:22+08:00 ][ log ] 【状态上报】开始处理地锁 '710001' 的通用状态...
log
[ 2025-08-11T15:20:22+08:00 ][ log ] 【状态上报】成功更新地锁 '710001' 的快照状态到数据库
log
[ 2025-08-11T15:20:22+08:00 ][ log ] 【状态上报】成功记录地锁 '710001' 的状态到日志表
log
[ 2025-08-11T15:20:22+08:00 ][ log ] 【业务触发】检查 '710001' 的状态变化...
log
[ 2025-08-11T15:20:22+08:00 ][ log ] 【业务触发】检测到地锁 '710001' 开锁成功，结束处理订单...
log
[ 2025-08-11T15:20:22+08:00 ][ log ] 打印order
log
[ 2025-08-11T15:20:22+08:00 ][ log ] array (
  'id' => 67,
  'platform_id' => 20,
  'agent_id' => 82,
  'hospital_id' => 48,
  'hospital_fcbl' => 20.0,
  'hospital_price' => '1.00',
  'hospital_hourlong' => 1,
  'hospital_freedt' => 999999,
  'departments_id' => 34,
  'equipment_id' => 2,
  'equipment_info_id' => 0,
  'info' => '总平台-深圳代理商-南山物业-物业北车棚-设备710001-710001',
  'sn' => 'ord2025081115195873971766',
  'user_id' => 5615,
  'money' => '0.00',
  'status' => '1',
  'pay_types' => '0',
  'pay_status' => '0',
  'pay_time' => NULL,
  'createtime' => 1754896802,
  'returntime' => NULL,
  'updatetime' => 1754896798,
  'is_branch' => '1',
  'admin_info' => NULL,
  'timelong' => 0,
  'timelong_fenzhong' => 0,
  'really_money' => '0.00',
  'actreturntime' => NULL,
  'answer_return_time' => 1754924400,
  'overtime' => NULL,
  'overtime_money' => NULL,
  'normal_money' => NULL,
  'charging_rule' => 1,
  'use_status' => 2,
  'nb_goodsid' => '',
)
log
[ 2025-08-11T15:20:22+08:00 ][ error ] 【业务触发】订单(SN:ord2025081115195873971766)结束逻辑处理成功
error
[ 2025-08-11T15:20:22+08:00 ][ error ] 收到消息：dz/pi/setack/710001
error
[ 2025-08-11T15:20:22+08:00 ][ error ] array (
  'topic' => 'dz/pi/setack/710001',
  'message' => '{"VER":"0","CMD":"17","CD":"710001","CL":"0","PACKID":50350,"STATUS":"1"}',
  'timestamp' => '2025-08-11 15:20:22',
)
error
[ 2025-08-11T15:20:22+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T15:20:22+08:00 ][ error ] array (
  'VER' => '0',
  'CMD' => '17',
  'CD' => '710001',
  'CL' => '0',
  'PACKID' => 50350,
  'STATUS' => '1',
)
error
[ 2025-08-11T15:20:22+08:00 ][ error ] 收到命令响应消息，待处理: dz/pi/setack/710001
error
[ 2025-08-11T15:20:22+08:00 ][ log ] 【命令响应】收到针对命令包 '50350' 的ACK确认
log
[ 2025-08-11T15:20:22+08:00 ][ log ] 【命令响应】成功更新命令 '50350' 的状态为 'acked'
log
[ 2025-08-11T15:20:29+08:00 ][ error ] 收到消息：dz/pi/getstatus/710002
error
[ 2025-08-11T15:20:29+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710002',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710002","SIMID":"898604F4152391140826","CS":"0","LS":"0","SS":"0","BS":"12.3","RSSI":28,"MT":"20","NG":"0"}',
  'timestamp' => '2025-08-11 15:20:29',
)
error
[ 2025-08-11T15:20:29+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T15:20:29+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710002',
  'SIMID' => '898604F4152391140826',
  'CS' => '0',
  'LS' => '0',
  'SS' => '0',
  'BS' => '12.3',
  'RSSI' => 28,
  'MT' => '20',
  'NG' => '0',
)
error
[ 2025-08-11T15:20:29+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710002
error
[ 2025-08-11T15:20:29+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-11T15:20:29+08:00 ][ log ] 【状态上报】开始处理地锁 '710002' 的通用状态...
log
[ 2025-08-11T15:20:29+08:00 ][ log ] 【状态上报】未在数据库中找到SN为 '710002' 的地锁设备
log
[ 2025-08-11T15:20:30+08:00 ][ error ] 收到消息：dz/pi/getstatus/710003
error
[ 2025-08-11T15:20:30+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710003',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710003","SIMID":"898604F4152391140839","CS":"0","LS":"0","SS":"0","BS":"12.3","RSSI":31,"MT":"20","NG":"0"}',
  'timestamp' => '2025-08-11 15:20:30',
)
error
[ 2025-08-11T15:20:30+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T15:20:30+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710003',
  'SIMID' => '898604F4152391140839',
  'CS' => '0',
  'LS' => '0',
  'SS' => '0',
  'BS' => '12.3',
  'RSSI' => 31,
  'MT' => '20',
  'NG' => '0',
)
error
[ 2025-08-11T15:20:30+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710003
error
[ 2025-08-11T15:20:30+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-11T15:20:30+08:00 ][ log ] 【状态上报】开始处理地锁 '710003' 的通用状态...
log
[ 2025-08-11T15:20:30+08:00 ][ log ] 【状态上报】未在数据库中找到SN为 '710003' 的地锁设备
log
[ 2025-08-11T15:22:50+08:00 ][ error ] 收到消息：dz/pi/getstatus/710001
error
[ 2025-08-11T15:22:50+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710001',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710001","SIMID":"89861118167312012425","CS":"0","LS":"1","SS":"0","BS":"10.1","RSSI":21,"MT":"4","NG":"0"}',
  'timestamp' => '2025-08-11 15:22:50',
)
error
[ 2025-08-11T15:22:50+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T15:22:50+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710001',
  'SIMID' => '89861118167312012425',
  'CS' => '0',
  'LS' => '1',
  'SS' => '0',
  'BS' => '10.1',
  'RSSI' => 21,
  'MT' => '4',
  'NG' => '0',
)
error
[ 2025-08-11T15:22:50+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710001
error
[ 2025-08-11T15:22:50+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-11T15:22:50+08:00 ][ log ] 【状态上报】开始处理地锁 '710001' 的通用状态...
log
[ 2025-08-11T15:22:50+08:00 ][ log ] 【状态上报】成功更新地锁 '710001' 的快照状态到数据库
log
[ 2025-08-11T15:22:50+08:00 ][ log ] 【状态上报】成功记录地锁 '710001' 的状态到日志表
log
[ 2025-08-11T15:22:50+08:00 ][ log ] 【业务触发】检查 '710001' 的状态变化...
log
[ 2025-08-11T15:22:50+08:00 ][ log ] 【业务触发】检测到地锁 '710001' 关锁成功，开始处理订单...
log
[ 2025-08-11T15:22:50+08:00 ][ error ] 收到消息：dz/pi/setack/710001
error
[ 2025-08-11T15:22:50+08:00 ][ error ] array (
  'topic' => 'dz/pi/setack/710001',
  'message' => '{"VER":"0","CMD":"17","CD":"710001","CL":"1","PACKID":2165,"STATUS":"81"}',
  'timestamp' => '2025-08-11 15:22:50',
)
error
[ 2025-08-11T15:22:50+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T15:22:50+08:00 ][ error ] array (
  'VER' => '0',
  'CMD' => '17',
  'CD' => '710001',
  'CL' => '1',
  'PACKID' => 2165,
  'STATUS' => '81',
)
error
[ 2025-08-11T15:22:50+08:00 ][ error ] 收到命令响应消息，待处理: dz/pi/setack/710001
error
[ 2025-08-11T15:22:50+08:00 ][ log ] 【命令响应】收到针对命令包 '2165' 的ACK确认
log
[ 2025-08-11T15:22:50+08:00 ][ log ] 【命令响应】成功更新命令 '2165' 的状态为 'acked'
log
[ 2025-08-11T15:23:21+08:00 ][ error ] 收到消息：dz/pi/getstatus/710001
error
[ 2025-08-11T15:23:21+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710001',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710001","SIMID":"89861118167312012425","CS":"0","LS":"0","SS":"0","BS":"11.3","RSSI":21,"MT":"4","NG":"0"}',
  'timestamp' => '2025-08-11 15:23:21',
)
error
[ 2025-08-11T15:23:21+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T15:23:21+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710001',
  'SIMID' => '89861118167312012425',
  'CS' => '0',
  'LS' => '0',
  'SS' => '0',
  'BS' => '11.3',
  'RSSI' => 21,
  'MT' => '4',
  'NG' => '0',
)
error
[ 2025-08-11T15:23:21+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710001
error
[ 2025-08-11T15:23:21+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-11T15:23:21+08:00 ][ log ] 【状态上报】开始处理地锁 '710001' 的通用状态...
log
[ 2025-08-11T15:23:21+08:00 ][ log ] 【状态上报】成功更新地锁 '710001' 的快照状态到数据库
log
[ 2025-08-11T15:23:21+08:00 ][ log ] 【状态上报】成功记录地锁 '710001' 的状态到日志表
log
[ 2025-08-11T15:23:21+08:00 ][ log ] 【业务触发】检查 '710001' 的状态变化...
log
[ 2025-08-11T15:23:21+08:00 ][ log ] 【业务触发】检测到地锁 '710001' 开锁成功，结束处理订单...
log
[ 2025-08-11T15:23:21+08:00 ][ log ] 打印order
log
[ 2025-08-11T15:23:21+08:00 ][ log ] array (
  'id' => 68,
  'platform_id' => 20,
  'agent_id' => 82,
  'hospital_id' => 48,
  'hospital_fcbl' => 20.0,
  'hospital_price' => '1.00',
  'hospital_hourlong' => 1,
  'hospital_freedt' => 999999,
  'departments_id' => 34,
  'equipment_id' => 2,
  'equipment_info_id' => 0,
  'info' => '总平台-深圳代理商-南山物业-物业北车棚-设备710001-710001',
  'sn' => 'ord2025081115224557851776',
  'user_id' => 5615,
  'money' => '0.00',
  'status' => '1',
  'pay_types' => '0',
  'pay_status' => '0',
  'pay_time' => NULL,
  'createtime' => 1754896970,
  'returntime' => NULL,
  'updatetime' => 1754896965,
  'is_branch' => '1',
  'admin_info' => NULL,
  'timelong' => 0,
  'timelong_fenzhong' => 0,
  'really_money' => '0.00',
  'actreturntime' => NULL,
  'answer_return_time' => 1754924400,
  'overtime' => NULL,
  'overtime_money' => NULL,
  'normal_money' => NULL,
  'charging_rule' => 1,
  'use_status' => 2,
  'nb_goodsid' => '',
)
log
[ 2025-08-11T15:23:21+08:00 ][ error ] 【业务触发】订单(SN:ord2025081115224557851776)结束逻辑处理成功
error
[ 2025-08-11T15:23:21+08:00 ][ error ] 收到消息：dz/pi/setack/710001
error
[ 2025-08-11T15:23:21+08:00 ][ error ] array (
  'topic' => 'dz/pi/setack/710001',
  'message' => '{"VER":"0","CMD":"17","CD":"710001","CL":"0","PACKID":43229,"STATUS":"1"}',
  'timestamp' => '2025-08-11 15:23:21',
)
error
[ 2025-08-11T15:23:21+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T15:23:21+08:00 ][ error ] array (
  'VER' => '0',
  'CMD' => '17',
  'CD' => '710001',
  'CL' => '0',
  'PACKID' => 43229,
  'STATUS' => '1',
)
error
[ 2025-08-11T15:23:21+08:00 ][ error ] 收到命令响应消息，待处理: dz/pi/setack/710001
error
[ 2025-08-11T15:23:21+08:00 ][ log ] 【命令响应】收到针对命令包 '43229' 的ACK确认
log
[ 2025-08-11T15:23:21+08:00 ][ log ] 【命令响应】成功更新命令 '43229' 的状态为 'acked'
log
[ 2025-08-11T15:30:27+08:00 ][ error ] 收到消息：dz/pi/getstatus/710002
error
[ 2025-08-11T15:30:27+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710002',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710002","SIMID":"898604F4152391140826","CS":"0","LS":"0","SS":"0","BS":"12.3","RSSI":28,"MT":"20","NG":"0"}',
  'timestamp' => '2025-08-11 15:30:27',
)
error
[ 2025-08-11T15:30:27+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T15:30:27+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710002',
  'SIMID' => '898604F4152391140826',
  'CS' => '0',
  'LS' => '0',
  'SS' => '0',
  'BS' => '12.3',
  'RSSI' => 28,
  'MT' => '20',
  'NG' => '0',
)
error
[ 2025-08-11T15:30:27+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710002
error
[ 2025-08-11T15:30:27+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-11T15:30:27+08:00 ][ log ] 【状态上报】开始处理地锁 '710002' 的通用状态...
log
[ 2025-08-11T15:30:27+08:00 ][ log ] 【状态上报】未在数据库中找到SN为 '710002' 的地锁设备
log
[ 2025-08-11T15:30:28+08:00 ][ error ] 收到消息：dz/pi/getstatus/710003
error
[ 2025-08-11T15:30:28+08:00 ][ error ] array (
  'topic' => 'dz/pi/getstatus/710003',
  'message' => '{"VER":"V1.250803","CMD":"12","CD":"710003","SIMID":"898604F4152391140839","CS":"0","LS":"0","SS":"0","BS":"12.3","RSSI":31,"MT":"20","NG":"0"}',
  'timestamp' => '2025-08-11 15:30:28',
)
error
[ 2025-08-11T15:30:28+08:00 ][ error ] 解析数据：
error
[ 2025-08-11T15:30:28+08:00 ][ error ] array (
  'VER' => 'V1.250803',
  'CMD' => '12',
  'CD' => '710003',
  'SIMID' => '898604F4152391140839',
  'CS' => '0',
  'LS' => '0',
  'SS' => '0',
  'BS' => '12.3',
  'RSSI' => 31,
  'MT' => '20',
  'NG' => '0',
)
error
[ 2025-08-11T15:30:28+08:00 ][ error ] 收到通用状态上报消息，待处理: dz/pi/getstatus/710003
error
[ 2025-08-11T15:30:28+08:00 ][ log ] 进入handleGetStatusMessage
log
[ 2025-08-11T15:30:28+08:00 ][ log ] 【状态上报】开始处理地锁 '710003' 的通用状态...
log
[ 2025-08-11T15:30:28+08:00 ][ log ] 【状态上报】未在数据库中找到SN为 '710003' 的地锁设备
log
