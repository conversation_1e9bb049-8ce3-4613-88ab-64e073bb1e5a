<!--packageA/rental/detail.wxml-->
<view class="container">
	<!-- 开锁成功时的简洁布局 -->
	<view wx:if="{{showSuccess}}" class="success-layout">
		<!-- 左上角返回按钮 -->
		<view class="back-btn" catch:tap="goBack" style="padding-top:{{statusBar}}px;">
			<text class="back-text">←</text>
		</view>

		<!-- 成功提示内容 -->
		<view class="success-main">
			<view class="success-icon">
				<text class="checkmark">✓</text>
			</view>
			<view class="success-text1">消防安全 人人有责</view>
			<view class="success-text2">地锁已打开，祝您一路平安</view>
		</view>
	</view>

	<!-- 正常状态的顶部区域 -->
	<view class="top" wx:if="{{!showSuccess}}">
		<view class="heard" style="padding-top:{{statusBar}}px;">
			<view class="back" catch:tap="goBack" style="height: {{statusBar}}px;top: {{statusBar}}px;"></view>
			<view class="title" style="line-height: {{statusBar}}px;">扫码归还</view>
		</view>
		<view class="location">
			<image src="/image/icon_dg.png"></image>
			<view>车锁已关闭！</view>
		</view>
		<view class="charge" data-path='/packageB/charge/index' bindtap='goUrl'>
			<image src="/image/icon_sfgl.png"></image>
			<view>收费标准</view>
		</view>

		<view class="hx"></view>
	</view>
	<!-- 开锁成功时显示按钮 -->
	<view class="success-btn-container" wx:if="{{showSuccess}}">
		<view wx:if="{{order.pay_status == 1}}" class="success-btn" bindtap="goPay">确认支付</view>
		<view wx:if="{{order.pay_status == 3}}" class="success-btn" bindtap="getPay">我要免单</view>
		<view wx:if="{{order.pay_status == 5}}" class="success-btn" bindtap="getPay">套餐抵扣</view>
	</view>

	<!-- 订单详情信息 -->
	<view class="detail">
		<view class="box">
			<view class="price">
				<view>¥</view>
				<text>{{order.money}}</text>
			</view>
			<view class="nr">
				<view class="title">车锁信息</view>
				<view class="xx">
					<view>物业名称：</view>
					<text>{{order.hospital_name}}</text>
				</view>
				<view class="xx">
					<view>车锁编号：</view>
					<text>{{order.equipment_name}}</text>
				</view>
				<view class="xx">
					<view>车锁地址：</view>
					<text>{{order.hospital_addr}}</text>
				</view>
			</view>
			<view class="nr">
				<view class="title">订单信息</view>
				<view class="xx">
					<view>开始时间：</view>
					<text>{{order.createtime}}</text>
				</view>
				<view class="xx">
					<view>结束时间：</view>
					<text>{{order.returntime}}</text>
				</view>
				<view class="xx">
					<view>租赁时长：</view>
					<text>{{order.timelong}}小时</text>
				</view>
				<view class="xx">
					<view>收费标准：</view>
					<text wx:if="{{order.charging_rule == 1}}">{{order.hospital_price*1 / order.hospital_hourlong*1}}元/小时</text>
					<text wx:else>{{order.contract_price}}元</text>
				</view>
				<view class="xx">
					<view>应付金额：</view>
					<text>￥{{order.money}}</text>
				</view>
			</view>
		</view>
		<view class="btn" wx:if="{{!showSuccess}}">
			<!-- <view bindtap="goPackage">购买套餐</view> -->
			<view wx:if="{{order.pay_status == 1}}" bindtap="goPay">确认支付</view>
			<view wx:if="{{order.pay_status == 3}}" bindtap="getPay">我要免单</view>
			<view wx:if="{{order.pay_status == 5}}" bindtap="getPay">套餐抵扣</view>
		</view>
	</view>
</view>